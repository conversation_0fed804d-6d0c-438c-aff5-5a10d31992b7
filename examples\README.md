# أمثلة محرك الألعاب Ring Game Engine

هذا المجلد يحتوي على مجموعة شاملة من الأمثلة التي توضح إمكانيات محرك الألعاب المختلفة.

## 📁 قائمة الأمثلة

### 🎮 [simple_game](simple_game/) - لعبة بسيطة
مثال شامل يوضح كيفية إنشاء لعبة كاملة باستخدام المحرك.

**الميزات المعروضة:**
- إنشاء المشاهد والكائنات
- نظام التحكم في اللاعب
- الذكاء الاصطناعي للأعداء
- جمع العناصر والنقاط
- نظام الجسيمات
- الإضاءة والمواد

**كيفية التشغيل:**
```bash
cd simple_game
ring simple_game.ring
```

### ⚡ [physics_demo](physics_demo/) - عرض الفيزياء
عرض تفاعلي لإمكانيات محرك الفيزياء.

**الميزات المعروضة:**
- محاكاة الأجسام الصلبة
- كشف التصادمات
- الجاذبية والقوى
- الارتداد والاحتكاك
- الانفجارات والتأثيرات

**التحكم:**
- `مسطرة المسافة`: إنشاء انفجار
- `R`: إعادة تعيين العرض
- `النقر بالفأرة`: إنشاء كرة
- `الفأرة الوسطى`: تحريك الكاميرا

### ✨ [particles_demo](particles_demo/) - عرض الجسيمات
عرض متنوع لأنواع مختلفة من تأثيرات الجسيمات.

**الميزات المعروضة:**
- تأثيرات النار والدخان
- انفجارات وشرارات
- تأثيرات الماء والسحر
- فيزياء الجسيمات
- تأثيرات الرياح

**التحكم:**
- `1-6`: اختيار التأثير
- `الأسهم`: التنقل بين التأثيرات
- `مسطرة المسافة`: تبديل جميع التأثيرات
- `R`: إعادة تشغيل التأثير الحالي
- `W/S`: تغيير الرياح

### 🤖 [ai_demo](ai_demo/) - عرض الذكاء الاصطناعي
عرض لأنواع مختلفة من السلوك الذكي للكائنات.

**الميزات المعروضة:**
- أشجار السلوك (Behavior Trees)
- آلات الحالة المحدودة
- أنواع مختلفة من الوكلاء الذكيين
- نظام الإدراك والرؤية
- التنقل والمطاردة

**أنواع الوكلاء:**
- **الحراس**: يحرسون مناطق معينة ويطاردون التهديدات
- **الدوريات**: يتنقلون بين نقاط محددة
- **الصيادين**: يبحثون عن أهداف ويطاردونها
- **المدنيين**: يتجولون عشوائياً ويهربون من الخطر

**التحكم:**
- `WASD`: تحريك اللاعب
- `الفأرة`: تحريك الكاميرا

## 🚀 كيفية تشغيل الأمثلة

### المتطلبات
- Ring Programming Language (الإصدار 1.18 أو أحدث)
- RayLib مثبتة ومكونة بشكل صحيح
- محرك الألعاب في المجلد الرئيسي

### خطوات التشغيل

1. **تأكد من تثبيت المتطلبات**
   ```bash
   # تحقق من تثبيت Ring
   ring --version
   ```

2. **انتقل لمجلد المثال المطلوب**
   ```bash
   cd examples/simple_game
   ```

3. **شغل المثال**
   ```bash
   ring simple_game.ring
   ```

## 📚 تعلم من الأمثلة

### للمبتدئين
ابدأ بـ **simple_game** لفهم الأساسيات:
- كيفية إنشاء محرك جديد
- إضافة كائنات للمشهد
- التعامل مع المدخلات
- إدارة دورة حياة اللعبة

### للمطورين المتوسطين
انتقل لـ **physics_demo** و **particles_demo** لتعلم:
- استخدام محرك الفيزياء
- إنشاء تأثيرات بصرية
- تحسين الأداء
- التفاعل مع البيئة

### للمطورين المتقدمين
اكتشف **ai_demo** لفهم:
- تصميم أنظمة الذكاء الاصطناعي
- أشجار السلوك المعقدة
- آلات الحالة المتقدمة
- تحسين سلوك الوكلاء

## 🛠️ تخصيص الأمثلة

### تعديل الإعدادات
يمكنك تعديل الإعدادات في بداية كل مثال:

```ring
# في simple_game.ring
oEngine = new Engine(1280, 720, "لعبتي المخصصة")

# تغيير سرعة اللاعب
oPlayerController.setMovementSpeed(8.0)

# تغيير عدد الأعداء
for i = 1 to 10 {  # بدلاً من 5
    oEnemy = createEnemy(i)
    # ...
}
```

### إضافة ميزات جديدة
```ring
# إضافة صوت للعبة
oEngine.getAudioEngine().playMusic("background_music.mp3")

# إضافة تأثيرات جديدة
oExplosionEmitter = oEngine.getParticleSystem().createEmitter("explosion")

# إضافة وكلاء ذكيين جدد
oNewAgent = new AIAgent("custom_type")
```

## 🎯 مشاريع مقترحة

بناءً على الأمثلة، يمكنك إنشاء:

### 🏃 لعبة منصات
- استخدم **simple_game** كأساس
- أضف قفز ومنصات
- أضف عوائق وفخاخ

### 🚗 لعبة سباق
- استخدم **physics_demo** للسيارات
- أضف مسارات وحلبات
- أضف تأثيرات الدخان والشرارات

### 🏰 لعبة استراتيجية
- استخدم **ai_demo** للوحدات
- أضف موارد وبناء
- أضف ذكاء اصطناعي للعدو

### 🌟 لعبة مغامرات
- ادمج جميع الأمثلة
- أضف قصة وحوار
- أضف نظام مهام

## 🤝 المساهمة

### إضافة أمثلة جديدة
1. أنشئ مجلد جديد في `examples/`
2. أضف الكود والملفات المطلوبة
3. أنشئ ملف README.md للمثال
4. أضف المثال لهذه القائمة

### تحسين الأمثلة الموجودة
1. أضف تعليقات توضيحية
2. حسن الأداء
3. أضف ميزات جديدة
4. أصلح الأخطاء

## 📞 الدعم

إذا واجهت مشاكل في تشغيل الأمثلة:

1. **تحقق من المتطلبات**: تأكد من تثبيت Ring و RayLib
2. **راجع الوثائق**: اقرأ دليل المطور
3. **اطلب المساعدة**: استخدم منتدى Ring أو GitHub Issues

## 📄 الترخيص

جميع الأمثلة مرخصة تحت نفس ترخيص المحرك (MIT License).

---

**استمتع بالتعلم والتطوير مع محرك الألعاب Ring Game Engine!** 🎮✨
