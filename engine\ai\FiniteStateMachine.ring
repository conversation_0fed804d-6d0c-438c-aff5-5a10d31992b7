/*
الكلاس: FiniteStateMachine
الوصف: آلة حالة محدودة للذكاء الاصطناعي
المدخلات: حالات وانتقالات
المخرجات: إدارة حالة ذكية
*/

class FiniteStateMachine {
    
    func init cMachineName = "StateMachine"
        cName = cMachineName
        cID = generateMachineID()
        aStates = []
        aStateMap = []  # خريطة سريعة للبحث
        oCurrentState = null
        oPreviousState = null
        bActive = true
        
        # إحصائيات الآلة
        nStateChanges = 0
        nUpdateCount = 0
        nCurrentStateTime = 0
        nLastUpdateTime = GetTime()

    func generateMachineID
        return "FSM_" + string(clock()) + "_" + string(random(99999))

    func addState cStateName, oState
        if oState != null {
            add(aStates, oState)
            aStateMap[cStateName] = oState
            oState.setStateMachine(this)
            oState.setName(cStateName)
            return true
        }
        return false

    func removeState cStateName
        oState = aStateMap[cStateName]
        if oState != null {
            nIndex = find(aStates, oState)
            if nIndex > 0 {
                del(aStates, nIndex)
                del(aStateMap, cStateName)
                
                # إذا كانت الحالة الحالية، قم بإيقافها
                if oCurrentState = oState {
                    oCurrentState.exit()
                    oCurrentState = null
                }
                
                return true
            }
        }
        return false

    func setState cStateName
        oNewState = aStateMap[cStateName]
        if oNewState != null and oNewState != oCurrentState {
            changeState(oNewState)
            return true
        }
        return false

    func setInitialState cStateName
        oState = aStateMap[cStateName]
        if oState != null {
            oCurrentState = oState
            oCurrentState.enter()
            nCurrentStateTime = 0
            return true
        }
        return false

    func changeState oNewState
        if oNewState = null or oNewState = oCurrentState {
            return
        }
        
        # الخروج من الحالة الحالية
        if oCurrentState != null {
            oCurrentState.exit()
            oPreviousState = oCurrentState
        }
        
        # الدخول للحالة الجديدة
        oCurrentState = oNewState
        oCurrentState.enter()
        
        # تحديث الإحصائيات
        nStateChanges++
        nCurrentStateTime = 0

    func update nDeltaTime
        if not bActive or oCurrentState = null {
            return
        }
        
        nCurrentTime = GetTime()
        nActualDeltaTime = nCurrentTime - nLastUpdateTime
        nLastUpdateTime = nCurrentTime
        
        # تحديث وقت الحالة الحالية
        nCurrentStateTime += nActualDeltaTime
        
        # تحديث الحالة الحالية
        oCurrentState.update(nDeltaTime)
        
        # فحص الانتقالات
        oNextState = oCurrentState.checkTransitions()
        if oNextState != null and oNextState != oCurrentState {
            changeState(oNextState)
        }
        
        nUpdateCount++

    func getCurrentState
        return oCurrentState

    func getCurrentStateName
        if oCurrentState != null {
            return oCurrentState.getName()
        }
        return ""

    func getPreviousState
        return oPreviousState

    func getPreviousStateName
        if oPreviousState != null {
            return oPreviousState.getName()
        }
        return ""

    func getCurrentStateTime
        return nCurrentStateTime

    func hasState cStateName
        return aStateMap[cStateName] != null

    func getState cStateName
        return aStateMap[cStateName]

    func setActive bState
        bActive = bState

    func isActive
        return bActive

    func getStatistics
        return [
            :name = cName,
            :id = cID,
            :currentState = getCurrentStateName(),
            :previousState = getPreviousStateName(),
            :currentStateTime = nCurrentStateTime,
            :stateChanges = nStateChanges,
            :updateCount = nUpdateCount,
            :stateCount = len(aStates)
        ]

    func reset
        # إعادة تعيين الآلة
        if oCurrentState != null {
            oCurrentState.exit()
        }
        oCurrentState = null
        oPreviousState = null
        nCurrentStateTime = 0
        nStateChanges = 0

    func cleanup
        # تنظيف الآلة
        if oCurrentState != null {
            oCurrentState.exit()
        }
        
        for oState in aStates {
            oState.cleanup()
        }
        
        aStates = []
        aStateMap = []
        oCurrentState = null
        oPreviousState = null

    func getName
        return cName

    func getID
        return cID

    private
        cName
        cID
        aStates
        aStateMap
        oCurrentState
        oPreviousState
        bActive
        nStateChanges
        nUpdateCount
        nCurrentStateTime
        nLastUpdateTime
}

# كلاس الحالة الأساسية
class State {
    
    func init cStateName = "State"
        cName = cStateName
        cID = generateStateID()
        oStateMachine = null
        aTransitions = []
        bActive = true
        
        # إحصائيات الحالة
        nEnterCount = 0
        nUpdateCount = 0
        nTotalTime = 0
        nLastEnterTime = 0

    func generateStateID
        return "STATE_" + string(clock()) + "_" + string(random(99999))

    func enter
        # يتم استدعاؤها عند دخول الحالة
        nEnterCount++
        nLastEnterTime = GetTime()
        onEnter()

    func update nDeltaTime
        # يتم استدعاؤها في كل إطار
        if bActive {
            nUpdateCount++
            onUpdate(nDeltaTime)
        }

    func exit
        # يتم استدعاؤها عند الخروج من الحالة
        if nLastEnterTime > 0 {
            nTotalTime += GetTime() - nLastEnterTime
        }
        onExit()

    func onEnter
        # للتنفيذ في الكلاسات المشتقة

    func onUpdate nDeltaTime
        # للتنفيذ في الكلاسات المشتقة

    func onExit
        # للتنفيذ في الكلاسات المشتقة

    func addTransition oTransition
        if oTransition != null {
            add(aTransitions, oTransition)
            oTransition.setFromState(this)
        }

    func removeTransition oTransition
        nIndex = find(aTransitions, oTransition)
        if nIndex > 0 {
            del(aTransitions, nIndex)
        }

    func checkTransitions
        # فحص جميع الانتقالات
        for oTransition in aTransitions {
            if oTransition.canTransition() {
                return oTransition.getToState()
            }
        }
        return null

    func setStateMachine oFSM
        oStateMachine = oFSM

    func getStateMachine
        return oStateMachine

    func setName cStateName
        cName = cStateName

    func getName
        return cName

    func getID
        return cID

    func setActive bState
        bActive = bState

    func isActive
        return bActive

    func getStatistics
        return [
            :name = cName,
            :id = cID,
            :enterCount = nEnterCount,
            :updateCount = nUpdateCount,
            :totalTime = nTotalTime,
            :averageTime = getAverageTime(),
            :transitionCount = len(aTransitions)
        ]

    func getAverageTime
        if nEnterCount = 0 {
            return 0.0
        }
        return nTotalTime / nEnterCount

    func cleanup
        aTransitions = []
        oStateMachine = null

    private
        cName
        cID
        oStateMachine
        aTransitions
        bActive
        nEnterCount
        nUpdateCount
        nTotalTime
        nLastEnterTime
}

# كلاس الانتقال
class Transition {
    
    func init oFromSt, oToSt, fpCondition
        oFromState = oFromSt
        oToState = oToSt
        fpConditionFunction = fpCondition
        cID = generateTransitionID()
        bActive = true

    func generateTransitionID
        return "TRANS_" + string(clock()) + "_" + string(random(99999))

    func canTransition
        if not bActive or fpConditionFunction = null {
            return false
        }
        
        return call fpConditionFunction()

    func setFromState oState
        oFromState = oState

    func getFromState
        return oFromState

    func setToState oState
        oToState = oState

    func getToState
        return oToState

    func setCondition fpCondition
        fpConditionFunction = fpCondition

    func setActive bState
        bActive = bState

    func isActive
        return bActive

    func getID
        return cID

    private
        cID
        oFromState
        oToState
        fpConditionFunction
        bActive
}
