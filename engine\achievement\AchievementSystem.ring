

class AchievementSystem {
    
    func init
        oUI = new AchievementUI
        oStorage = new AchievementStorage
        loadAchievements()
        loadProgress()

    func loadAchievements
        cPath = "data/achievements.json"
        try {
            cContent = read(cPath)
            oData = JSON.decode(cContent)
            
            for oAchData in oData {
                add(aAchievements, new Achievement {
                    cID = oAchData["id"]
                    cTitle = oAchData["title"]
                    cDescription = oAchData["description"]
                    cIcon = oAchData["icon"]
                    nPoints = oAchData["points"]
                    aRequirements = oAchData["requirements"]
                    bSecret = oAchData["secret"]
                })
            }
        catch 
            log("Error loading achievements: " + cCatchError)
        }

    func loadProgress
        aUnlocked = oStorage.load()
        for cAchID in aUnlocked {
            oAch = findAchievement(cAchID)
            if oAch {
                oAch.unlock()
            }
        }

    func saveProgress
        oStorage.save(aUnlocked)

    func checkAchievement cID
        oAch = findAchievement(cID)
        if oAch and not oAch.isUnlocked() {
            if checkRequirements(oAch) {
                unlockAchievement(oAch)
            }
        }

    func unlockAchievement oAch
        if not oAch.isUnlocked() {
            oAch.unlock()
            add(aUnlocked, oAch.cID)
            saveProgress()
            showUnlockNotification(oAch)
        }

    func showUnlockNotification oAch
        oUI.showNotification(oAch.cTitle, oAch.cDescription, 
                           oAch.cIcon, oAch.nPoints)

    func getProgress
        nTotal = len(aAchievements)
        nUnlocked = len(aUnlocked)
        return {
            "total": nTotal,
            "unlocked": nUnlocked,
            "percentage": (nUnlocked / nTotal) * 100
        }

    func getTotalPoints
        nPoints = 0
        for oAch in aAchievements {
            if oAch.isUnlocked() {
                nPoints += oAch.nPoints
            }
        }
        return nPoints

    private

        aAchievements = []
        aUnlocked = []
        oUI
        oStorage

    func findAchievement cID
        for oAch in aAchievements {
            if oAch.cID = cID {
                return oAch
            }
        }
        return null

    func checkRequirements oAch
        for oReq in oAch.aRequirements {
            if not evaluateRequirement(oReq) {
                return false
            }
        }
        return true

    func evaluateRequirement oReq
        switch oReq.type {
            case "quest"
                return isQuestCompleted(oReq.questID)
            
            case "level"
                return getPlayerLevel() >= oReq.level
            
            case "item"
                return hasItem(oReq.itemID, oReq.quantity)
            
            case "kill"
                return getKillCount(oReq.enemyType) >= oReq.count
            
            case "explore"
                return hasExploredLocation(oReq.locationID)
            
            case "skill"
                return getSkillLevel(oReq.skillID) >= oReq.level
        }
        return false
}

class Achievement {
    

    func init
        # Constructor implementation

    func unlock
        if not bUnlocked {
            bUnlocked = true
            dUnlockDate = date()
        }

    func isUnlocked
        return bUnlocked

    func isSecret
        return bSecret

    func getProgress
        if bUnlocked {
            return 100
        }
        
        nTotal = len(aRequirements)
        nCompleted = 0
        
        for oReq in aRequirements {
            if evaluateRequirement(oReq) {
                nCompleted++
            }
        }
        
        return (nCompleted / nTotal) * 100

    private 
        cID
        cTitle
        cDescription
        cIcon
        nPoints
        aRequirements
        bSecret
        bUnlocked = false
        dUnlockDate
    
}
