/*
الكلاس: ResourceManager
الوصف: إدارة تحميل وتخزين وإلغاء تحميل موارد اللعبة
المدخلات: مسارات الملفات وأنواع الموارد
المخرجات: موارد محملة ومُدارة بكفاءة
*/

load "raylib.ring"

class ResourceManager {
    
    func init
        aTextures = []
        aModels = []
        aSounds = []
        aMusic = []
        aShaders = []
        aFonts = []
        aMaterials = []
        
        # إعداد مسارات الموارد الافتراضية
        setupDefaultPaths()

    func setupDefaultPaths
        cTexturesPath = "assets/textures/"
        cModelsPath = "assets/models/"
        cSoundsPath = "assets/sounds/"
        cMusicPath = "assets/music/"
        cShadersPath = "assets/shaders/"
        cFontsPath = "assets/fonts/"

    func loadTexture cPath, cName = ""
        if cName = "" {
            cName = extractFileName(cPath)
        }
        
        # التحقق من وجود الملف مسبقاً
        oExisting = findTexture(cName)
        if oExisting != null {
            return oExisting
        }
        
        # تحميل القوام
        cFullPath = cTexturesPath + cPath
        oTexture = LoadTexture(cFullPath)
        
        if IsTextureReady(oTexture) {
            oResource = new TextureResource(oTexture, cName, cFullPath)
            add(aTextures, oResource)
            return oTexture
        else
            ? "خطأ في تحميل القوام: " + cFullPath
            return null
        }

    func loadModel cPath, cName = ""
        if cName = "" {
            cName = extractFileName(cPath)
        }
        
        oExisting = findModel(cName)
        if oExisting != null {
            return oExisting
        }
        
        cFullPath = cModelsPath + cPath
        oModel = LoadModel(cFullPath)
        
        if IsModelReady(oModel) {
            oResource = new ModelResource(oModel, cName, cFullPath)
            add(aModels, oResource)
            return oModel
        else
            ? "خطأ في تحميل النموذج: " + cFullPath
            return null
        }

    func loadSound cPath, cName = ""
        if cName = "" {
            cName = extractFileName(cPath)
        }
        
        oExisting = findSound(cName)
        if oExisting != null {
            return oExisting
        }
        
        cFullPath = cSoundsPath + cPath
        oSound = LoadSound(cFullPath)
        
        if IsSoundReady(oSound) {
            oResource = new SoundResource(oSound, cName, cFullPath)
            add(aSounds, oResource)
            return oSound
        else
            ? "خطأ في تحميل الصوت: " + cFullPath
            return null
        }

    func loadMusic cPath, cName = ""
        if cName = "" {
            cName = extractFileName(cPath)
        }
        
        oExisting = findMusic(cName)
        if oExisting != null {
            return oExisting
        }
        
        cFullPath = cMusicPath + cPath
        oMusicStream = LoadMusicStream(cFullPath)
        
        if IsMusicReady(oMusicStream) {
            oResource = new MusicResource(oMusicStream, cName, cFullPath)
            add(aMusic, oResource)
            return oMusicStream
        else
            ? "خطأ في تحميل الموسيقى: " + cFullPath
            return null
        }

    func loadFont cPath, cName = "", nSize = 32
        if cName = "" {
            cName = extractFileName(cPath)
        }
        
        oExisting = findFont(cName)
        if oExisting != null {
            return oExisting
        }
        
        cFullPath = cFontsPath + cPath
        oFont = LoadFontEx(cFullPath, nSize, null, 0)
        
        if IsFontReady(oFont) {
            oResource = new FontResource(oFont, cName, cFullPath)
            add(aFonts, oResource)
            return oFont
        else
            ? "خطأ في تحميل الخط: " + cFullPath
            return null
        }

    func findTexture cName
        for oResource in aTextures {
            if oResource.getName() = cName {
                return oResource.getResource()
            }
        }
        return null

    func findModel cName
        for oResource in aModels {
            if oResource.getName() = cName {
                return oResource.getResource()
            }
        }
        return null

    func findSound cName
        for oResource in aSounds {
            if oResource.getName() = cName {
                return oResource.getResource()
            }
        }
        return null

    func findMusic cName
        for oResource in aMusic {
            if oResource.getName() = cName {
                return oResource.getResource()
            }
        }
        return null

    func findFont cName
        for oResource in aFonts {
            if oResource.getName() = cName {
                return oResource.getResource()
            }
        }
        return null

    func unloadTexture cName
        nIndex = 0
        for i = 1 to len(aTextures) {
            if aTextures[i].getName() = cName {
                UnloadTexture(aTextures[i].getResource())
                nIndex = i
                break
            }
        }
        if nIndex > 0 {
            del(aTextures, nIndex)
        }

    func unloadModel cName
        nIndex = 0
        for i = 1 to len(aModels) {
            if aModels[i].getName() = cName {
                UnloadModel(aModels[i].getResource())
                nIndex = i
                break
            }
        }
        if nIndex > 0 {
            del(aModels, nIndex)
        }

    func unloadSound cName
        nIndex = 0
        for i = 1 to len(aSounds) {
            if aSounds[i].getName() = cName {
                UnloadSound(aSounds[i].getResource())
                nIndex = i
                break
            }
        }
        if nIndex > 0 {
            del(aSounds, nIndex)
        }

    func extractFileName cPath
        # استخراج اسم الملف من المسار
        aPathParts = split(cPath, "/")
        cFileName = aPathParts[len(aPathParts)]
        aNameParts = split(cFileName, ".")
        return aNameParts[1]

    func cleanup
        # تحرير جميع الموارد
        for oResource in aTextures {
            UnloadTexture(oResource.getResource())
        }
        for oResource in aModels {
            UnloadModel(oResource.getResource())
        }
        for oResource in aSounds {
            UnloadSound(oResource.getResource())
        }
        for oResource in aMusic {
            UnloadMusicStream(oResource.getResource())
        }
        for oResource in aFonts {
            UnloadFont(oResource.getResource())
        }
        
        aTextures = []
        aModels = []
        aSounds = []
        aMusic = []
        aFonts = []

    func getLoadedResourcesCount
        return len(aTextures) + len(aModels) + len(aSounds) + len(aMusic) + len(aFonts)

    func getMemoryUsage
        # تقدير استخدام الذاكرة (تقريبي)
        nMemory = 0
        nMemory += len(aTextures) * 1024  # تقدير متوسط للقوام
        nMemory += len(aModels) * 2048    # تقدير متوسط للنماذج
        nMemory += len(aSounds) * 512     # تقدير متوسط للأصوات
        nMemory += len(aMusic) * 4096     # تقدير متوسط للموسيقى
        return nMemory

    private
        aTextures
        aModels
        aSounds
        aMusic
        aShaders
        aFonts
        aMaterials
        cTexturesPath
        cModelsPath
        cSoundsPath
        cMusicPath
        cShadersPath
        cFontsPath
}
