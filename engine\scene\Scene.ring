/*
الكلاس: Scene
الوصف: يمثل مشهد واحد في اللعبة يحتوي على كائنات وإعدادات
المدخلات: اسم المشهد والكائنات
المخرجات: مشهد قابل للتحديث والرسم
*/

load "raylib.ring"
load "../core/GameObject.ring"

class Scene {
    
    func init cSceneName = "NewScene"
        cName = cSceneName
        cID = generateSceneID()
        aGameObjects = []
        aLights = []
        aCameras = []
        oActiveCamera = null
        oSceneGraph = new SceneGraph()
        
        # إعدادات المشهد
        aAmbientColor = [0.2, 0.2, 0.2, 1.0]
        aSkyboxColor = [0.5, 0.8, 1.0, 1.0]
        bFogEnabled = false
        nFogDensity = 0.01
        aFogColor = [0.7, 0.7, 0.7, 1.0]
        
        # إحصائيات المشهد
        nObjectCount = 0
        nTriangleCount = 0
        
        setupDefaultScene()

    func generateSceneID
        return "SCENE_" + string(clock()) + "_" + string(random(9999))

    func setupDefaultScene
        # إنشاء كاميرا افتراضية
        oDefaultCamera = new Camera3D(
            [0, 10, 10],    # الموقع
            [0, 0, 0],      # الهدف
            [0, 1, 0],      # الاتجاه العلوي
            45,             # زاوية الرؤية
            CAMERA_PERSPECTIVE
        )
        addCamera(oDefaultCamera)
        setActiveCamera(oDefaultCamera)
        
        # إضافة إضاءة افتراضية
        oDefaultLight = new Light3D([0, 10, 0], WHITE, LIGHT_DIRECTIONAL)
        addLight(oDefaultLight)

    func addGameObject oObject
        if oObject != null and find(aGameObjects, oObject) = 0 {
            add(aGameObjects, oObject)
            oSceneGraph.addNode(oObject)
            nObjectCount++
            return true
        }
        return false

    func removeGameObject oObject
        nIndex = find(aGameObjects, oObject)
        if nIndex > 0 {
            del(aGameObjects, nIndex)
            oSceneGraph.removeNode(oObject)
            nObjectCount--
            return true
        }
        return false

    func findGameObject cName
        for oObject in aGameObjects {
            if oObject.getName() = cName {
                return oObject
            }
        }
        return null

    func findGameObjectByID cID
        for oObject in aGameObjects {
            if oObject.getID() = cID {
                return oObject
            }
        }
        return null

    func addLight oLight
        if oLight != null and find(aLights, oLight) = 0 {
            add(aLights, oLight)
            return true
        }
        return false

    func removeLight oLight
        nIndex = find(aLights, oLight)
        if nIndex > 0 {
            del(aLights, nIndex)
            return true
        }
        return false

    func addCamera oCamera
        if oCamera != null and find(aCameras, oCamera) = 0 {
            add(aCameras, oCamera)
            return true
        }
        return false

    func setActiveCamera oCamera
        if find(aCameras, oCamera) > 0 {
            oActiveCamera = oCamera
            return true
        }
        return false

    func getActiveCamera
        return oActiveCamera

    func update nDeltaTime
        # تحديث جميع كائنات المشهد
        for oObject in aGameObjects {
            if oObject.isActive() {
                oObject.update(nDeltaTime)
            }
        }
        
        # تحديث الرسم البياني للمشهد
        oSceneGraph.update()
        
        # تحديث الإحصائيات
        updateStatistics()

    func render
        # رسم جميع كائنات المشهد
        for oObject in aGameObjects {
            if oObject.isVisible() {
                oObject.render()
            }
        }

    func updateStatistics
        # تحديث إحصائيات المشهد
        nTriangleCount = 0
        for oObject in aGameObjects {
            # حساب عدد المثلثات (تقديري)
            nTriangleCount += 100  # قيمة افتراضية
        }

    func load cFilePath
        # تحميل المشهد من ملف
        try {
            cData = read(cFilePath)
            oSceneData = JSON.decode(cData)
            loadFromData(oSceneData)
            return true
        catch
            ? "خطأ في تحميل المشهد: " + cFilePath
            return false
        }

    func save cFilePath = ""
        # حفظ المشهد إلى ملف
        if cFilePath = "" {
            cFilePath = "scenes/" + cName + ".json"
        }
        
        try {
            oSceneData = serializeScene()
            cData = JSON.encode(oSceneData)
            write(cFilePath, cData)
            return true
        catch
            ? "خطأ في حفظ المشهد: " + cFilePath
            return false
        }

    func serializeScene
        # تحويل المشهد إلى بيانات قابلة للحفظ
        oData = [
            :name = cName,
            :id = cID,
            :ambientColor = aAmbientColor,
            :skyboxColor = aSkyboxColor,
            :fogEnabled = bFogEnabled,
            :fogDensity = nFogDensity,
            :fogColor = aFogColor,
            :objects = [],
            :lights = [],
            :cameras = []
        ]
        
        # تسلسل الكائنات
        for oObject in aGameObjects {
            add(oData[:objects], serializeGameObject(oObject))
        }
        
        return oData

    func serializeGameObject oObject
        # تحويل كائن اللعبة إلى بيانات
        return [
            :name = oObject.getName(),
            :id = oObject.getID(),
            :position = oObject.getPosition(),
            :rotation = oObject.getRotation(),
            :scale = oObject.getScale(),
            :active = oObject.isActive(),
            :visible = oObject.isVisible()
        ]

    func loadFromData oData
        # تحميل المشهد من البيانات
        cName = oData[:name]
        cID = oData[:id]
        aAmbientColor = oData[:ambientColor]
        aSkyboxColor = oData[:skyboxColor]
        bFogEnabled = oData[:fogEnabled]
        nFogDensity = oData[:fogDensity]
        aFogColor = oData[:fogColor]
        
        # تحميل الكائنات
        for oObjectData in oData[:objects] {
            oObject = createGameObjectFromData(oObjectData)
            addGameObject(oObject)
        }

    func createGameObjectFromData oData
        # إنشاء كائن لعبة من البيانات
        oObject = new GameObject(oData[:name])
        oObject.setPosition(oData[:position])
        oObject.setRotation(oData[:rotation])
        oObject.setScale(oData[:scale])
        oObject.setActive(oData[:active])
        oObject.setVisible(oData[:visible])
        return oObject

    func onEnter
        # يتم استدعاؤها عند دخول المشهد
        ? "دخول المشهد: " + cName

    func onExit
        # يتم استدعاؤها عند الخروج من المشهد
        ? "الخروج من المشهد: " + cName

    func cleanup
        # تنظيف موارد المشهد
        for oObject in aGameObjects {
            oObject.destroy()
        }
        aGameObjects = []
        aLights = []
        aCameras = []
        oSceneGraph.cleanup()

    func getName
        return cName

    func setName cNewName
        cName = cNewName

    func getID
        return cID

    func getObjectCount
        return nObjectCount

    func getTriangleCount
        return nTriangleCount

    func getGameObjects
        return aGameObjects

    func getLights
        return aLights

    func getCameras
        return aCameras

    private
        cName
        cID
        aGameObjects
        aLights
        aCameras
        oActiveCamera
        oSceneGraph
        aAmbientColor
        aSkyboxColor
        bFogEnabled
        nFogDensity
        aFogColor
        nObjectCount
        nTriangleCount
}
