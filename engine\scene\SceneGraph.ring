/*
الكلاس: SceneGraph
الوصف: هيكل شجري لتنظيم كائنات المشهد وإدارة العلاقات الهرمية
المدخلات: كائنات اللعبة والعلاقات بينها
المخرجات: هيكل منظم وقابل للبحث والتحديث
*/

class SceneGraph {
    
    func init
        oRootNode = new SceneNode(null, "Root")
        aAllNodes = []
        aNodeMap = []  # خريطة سريعة للبحث بالمعرف

    func addNode oGameObject, cParentID = ""
        if oGameObject = null {
            return false
        }
        
        # إنشاء عقدة جديدة
        oNewNode = new SceneNode(oGameObject, oGameObject.getID())
        
        # العثور على العقدة الوالدة
        oParentNode = oRootNode
        if cParentID != "" {
            oParentNode = findNodeByID(cParentID)
            if oParentNode = null {
                oParentNode = oRootNode
            }
        }
        
        # إضافة العقدة للوالد
        oParentNode.addChild(oNewNode)
        oNewNode.setParent(oParentNode)
        
        # إضافة للقوائم
        add(aAllNodes, oNewNode)
        aNodeMap[oGameObject.getID()] = oNewNode
        
        return true

    func removeNode oGameObject
        if oGameObject = null {
            return false
        }
        
        cID = oGameObject.getID()
        oNode = aNodeMap[cID]
        
        if oNode != null {
            # إزالة من الوالد
            oParent = oNode.getParent()
            if oParent != null {
                oParent.removeChild(oNode)
            }
            
            # نقل الأطفال للوالد الجد
            aChildren = oNode.getChildren()
            for oChild in aChildren {
                oParent.addChild(oChild)
                oChild.setParent(oParent)
            }
            
            # إزالة من القوائم
            nIndex = find(aAllNodes, oNode)
            if nIndex > 0 {
                del(aAllNodes, nIndex)
            }
            del(aNodeMap, cID)
            
            return true
        }
        
        return false

    func findNode cID
        return aNodeMap[cID]

    func findNodeByID cID
        return aNodeMap[cID]

    func findNodesByName cName
        aResults = []
        findNodesByNameRecursive(oRootNode, cName, aResults)
        return aResults

    func findNodesByNameRecursive oNode, cName, aResults
        if oNode.getGameObject() != null and oNode.getGameObject().getName() = cName {
            add(aResults, oNode)
        }
        
        for oChild in oNode.getChildren() {
            findNodesByNameRecursive(oChild, cName, aResults)
        }

    func update
        # تحديث جميع العقد بدءاً من الجذر
        updateNodeRecursive(oRootNode)

    func updateNodeRecursive oNode
        # تحديث العقدة الحالية
        if oNode.getGameObject() != null {
            oNode.updateTransform()
        }
        
        # تحديث الأطفال
        for oChild in oNode.getChildren() {
            updateNodeRecursive(oChild)
        }

    func render
        # رسم جميع العقد بدءاً من الجذر
        renderNodeRecursive(oRootNode)

    func renderNodeRecursive oNode
        # رسم العقدة الحالية
        if oNode.getGameObject() != null and oNode.getGameObject().isVisible() {
            oNode.getGameObject().render()
        }
        
        # رسم الأطفال
        for oChild in oNode.getChildren() {
            renderNodeRecursive(oChild)
        }

    func getVisibleNodes oCamera
        # الحصول على العقد المرئية للكاميرا
        aVisibleNodes = []
        getVisibleNodesRecursive(oRootNode, oCamera, aVisibleNodes)
        return aVisibleNodes

    func getVisibleNodesRecursive oNode, oCamera, aVisibleNodes
        if oNode.getGameObject() != null {
            if isNodeVisible(oNode, oCamera) {
                add(aVisibleNodes, oNode)
            }
        }
        
        for oChild in oNode.getChildren() {
            getVisibleNodesRecursive(oChild, oCamera, aVisibleNodes)
        }

    func isNodeVisible oNode, oCamera
        # فحص مبسط للرؤية (يمكن تحسينه لاحقاً)
        oGameObject = oNode.getGameObject()
        if oGameObject = null or not oGameObject.isVisible() {
            return false
        }
        
        # فحص المسافة من الكاميرا
        aObjectPos = oGameObject.getPosition()
        aCameraPos = [0, 0, 0]  # يجب الحصول على موقع الكاميرا الفعلي
        nDistance = calculateDistance(aObjectPos, aCameraPos)
        
        # إذا كان الكائن قريب جداً أو بعيد جداً
        if nDistance < 0.1 or nDistance > 1000 {
            return false
        }
        
        return true

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDy = aPos1[2] - aPos2[2]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

    func getNodeCount
        return len(aAllNodes)

    func getRootNode
        return oRootNode

    func getAllNodes
        return aAllNodes

    func cleanup
        # تنظيف جميع العقد
        for oNode in aAllNodes {
            oNode.cleanup()
        }
        aAllNodes = []
        aNodeMap = []
        oRootNode = new SceneNode(null, "Root")

    private
        oRootNode
        aAllNodes
        aNodeMap
}

# كلاس عقدة المشهد
class SceneNode {
    
    func init oGameObj, cNodeID
        oGameObject = oGameObj
        cID = cNodeID
        oParent = null
        aChildren = []
        mLocalTransform = MatrixIdentity()
        mWorldTransform = MatrixIdentity()
        bTransformDirty = true

    func addChild oChildNode
        if oChildNode != null and find(aChildren, oChildNode) = 0 {
            add(aChildren, oChildNode)
            oChildNode.setParent(this)
            markTransformDirty()
        }

    func removeChild oChildNode
        nIndex = find(aChildren, oChildNode)
        if nIndex > 0 {
            del(aChildren, nIndex)
            oChildNode.setParent(null)
        }

    func setParent oParentNode
        oParent = oParentNode
        markTransformDirty()

    func getParent
        return oParent

    func getChildren
        return aChildren

    func getGameObject
        return oGameObject

    func getID
        return cID

    func updateTransform
        if bTransformDirty {
            # حساب التحويل المحلي
            if oGameObject != null {
                mLocalTransform = oGameObject.getTransform()
            }
            
            # حساب التحويل العالمي
            if oParent != null and oParent.getGameObject() != null {
                mWorldTransform = MatrixMultiply(mLocalTransform, oParent.getWorldTransform())
            else
                mWorldTransform = mLocalTransform
            }
            
            bTransformDirty = false
        }

    func getLocalTransform
        return mLocalTransform

    func getWorldTransform
        return mWorldTransform

    func markTransformDirty
        bTransformDirty = true
        # تمييز الأطفال كمتسخين أيضاً
        for oChild in aChildren {
            oChild.markTransformDirty()
        }

    func cleanup
        oGameObject = null
        oParent = null
        aChildren = []

    private
        oGameObject
        cID
        oParent
        aChildren
        mLocalTransform
        mWorldTransform
        bTransformDirty
}
