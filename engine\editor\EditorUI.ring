/*
الكلاس: EditorUI
الوصف: واجهة المستخدم الرسومية لمحرر المشاهد
المدخلات: لوحات وأدوات المحرر
المخرجات: واجهة مستخدم تفاعلية
*/

load "raylib.ring"

class EditorUI {
    
    func init
        # اللوحات
        aPanels = []
        aPanelMap = []
        
        # شريط الأدوات والقوائم
        oToolbar = null
        oMenuBar = null
        oStatusBar = null
        
        # إعدادات الواجهة
        nPanelWidth = 300
        nToolbarHeight = 40
        nMenuBarHeight = 25
        nStatusBarHeight = 20
        
        # الألوان والأنماط
        oPanelColor = Color(45, 45, 48, 255)
        oHeaderColor = Color(37, 37, 38, 255)
        oTextColor = WHITE
        oAccentColor = Color(0, 122, 204, 255)
        
        # حالة الواجهة
        bInitialized = false
        bVisible = true
        
        # النوافذ المنبثقة
        aDialogs = []
        oActiveDialog = null

    func initialize
        ? "تهيئة واجهة المحرر..."
        
        # إنشاء شريط القوائم
        createMenuBar()
        
        # إنشاء شريط الأدوات
        createToolbar()
        
        # إنشاء شريط الحالة
        createStatusBar()
        
        bInitialized = true
        ? "تم تهيئة واجهة المحرر"

    func createMenuBar
        oMenuBar = new EditorMenuBar()
        oMenuBar.addMenu("File", [
            "New Scene|Ctrl+N",
            "Open Scene|Ctrl+O",
            "Save Scene|Ctrl+S",
            "Save As...|Ctrl+Shift+S",
            "-",
            "Import Model",
            "Export Scene",
            "-",
            "Exit|Alt+F4"
        ])
        
        oMenuBar.addMenu("Edit", [
            "Undo|Ctrl+Z",
            "Redo|Ctrl+Y",
            "-",
            "Cut|Ctrl+X",
            "Copy|Ctrl+C",
            "Paste|Ctrl+V",
            "Duplicate|Ctrl+D",
            "Delete|Del",
            "-",
            "Select All|Ctrl+A"
        ])
        
        oMenuBar.addMenu("GameObject", [
            "Create Empty",
            "-",
            "3D Object>Cube",
            "3D Object>Sphere",
            "3D Object>Plane",
            "3D Object>Cylinder",
            "-",
            "Light>Directional Light",
            "Light>Point Light",
            "Light>Spot Light",
            "-",
            "Camera",
            "Particle System"
        ])
        
        oMenuBar.addMenu("View", [
            "Toggle Grid|G",
            "Toggle Gizmos",
            "Toggle Stats",
            "-",
            "Focus Selected|F",
            "Frame All",
            "-",
            "Wireframe Mode",
            "Solid Mode",
            "Textured Mode"
        ])
        
        oMenuBar.addMenu("Window", [
            "Hierarchy",
            "Inspector",
            "Project",
            "Console",
            "-",
            "Reset Layout"
        ])

    func createToolbar
        oToolbar = new EditorToolbar()
        
        # أدوات التحديد والتحرير
        oToolbar.addTool("select", "Select", "Q")
        oToolbar.addTool("move", "Move", "W")
        oToolbar.addTool("rotate", "Rotate", "E")
        oToolbar.addTool("scale", "Scale", "R")
        
        oToolbar.addSeparator()
        
        # أدوات العرض
        oToolbar.addToggle("grid", "Grid", "G", true)
        oToolbar.addToggle("gizmos", "Gizmos", "", true)
        oToolbar.addToggle("stats", "Stats", "", false)
        
        oToolbar.addSeparator()
        
        # أدوات التشغيل
        oToolbar.addTool("play", "Play", "Space")
        oToolbar.addTool("pause", "Pause", "")
        oToolbar.addTool("stop", "Stop", "")

    func createStatusBar
        oStatusBar = new EditorStatusBar()

    func addPanel cName, oPanel
        if oPanel != null {
            add(aPanels, oPanel)
            aPanelMap[cName] = oPanel
            oPanel.setName(cName)
            oPanel.setUI(this)
        }

    func removePanel cName
        oPanel = aPanelMap[cName]
        if oPanel != null {
            nIndex = find(aPanels, oPanel)
            if nIndex > 0 {
                del(aPanels, nIndex)
            }
            del(aPanelMap, cName)
        }

    func getPanel cName
        return aPanelMap[cName]

    func setToolbar oTB
        oToolbar = oTB

    func setMenuBar oMB
        oMenuBar = oMB

    func update nDeltaTime
        if not bInitialized or not bVisible {
            return
        }
        
        # تحديث اللوحات
        for oPanel in aPanels {
            if oPanel.isVisible() {
                oPanel.update(nDeltaTime)
            }
        }
        
        # تحديث شريط الأدوات
        if oToolbar != null {
            oToolbar.update(nDeltaTime)
        }
        
        # تحديث النوافذ المنبثقة
        updateDialogs(nDeltaTime)

    func updateDialogs nDeltaTime
        for oDialog in aDialogs {
            if oDialog.isVisible() {
                oDialog.update(nDeltaTime)
            }
        }

    func render
        if not bInitialized or not bVisible {
            return
        }
        
        # رسم شريط القوائم
        if oMenuBar != null {
            renderMenuBar()
        }
        
        # رسم شريط الأدوات
        if oToolbar != null {
            renderToolbar()
        }
        
        # رسم اللوحات
        renderPanels()
        
        # رسم شريط الحالة
        if oStatusBar != null {
            renderStatusBar()
        }
        
        # رسم النوافذ المنبثقة
        renderDialogs()

    func renderMenuBar
        nScreenWidth = GetScreenWidth()
        DrawRectangle(0, 0, nScreenWidth, nMenuBarHeight, oHeaderColor)
        oMenuBar.render(0, 0, nScreenWidth, nMenuBarHeight)

    func renderToolbar
        nScreenWidth = GetScreenWidth()
        nY = nMenuBarHeight
        DrawRectangle(0, nY, nScreenWidth, nToolbarHeight, oPanelColor)
        oToolbar.render(0, nY, nScreenWidth, nToolbarHeight)

    func renderPanels
        nScreenWidth = GetScreenWidth()
        nScreenHeight = GetScreenHeight()
        nContentY = nMenuBarHeight + nToolbarHeight
        nContentHeight = nScreenHeight - nContentY - nStatusBarHeight
        
        # رسم اللوحة اليسرى (Hierarchy)
        oHierarchyPanel = getPanel("Hierarchy")
        if oHierarchyPanel != null and oHierarchyPanel.isVisible() {
            DrawRectangle(0, nContentY, nPanelWidth, nContentHeight, oPanelColor)
            oHierarchyPanel.render(0, nContentY, nPanelWidth, nContentHeight)
        }
        
        # رسم اللوحة اليمنى (Inspector)
        oInspectorPanel = getPanel("Inspector")
        if oInspectorPanel != null and oInspectorPanel.isVisible() {
            nX = nScreenWidth - nPanelWidth
            DrawRectangle(nX, nContentY, nPanelWidth, nContentHeight, oPanelColor)
            oInspectorPanel.render(nX, nContentY, nPanelWidth, nContentHeight)
        }
        
        # رسم اللوحة السفلى (Project/Console)
        nBottomPanelHeight = 200
        nBottomY = nScreenHeight - nStatusBarHeight - nBottomPanelHeight
        nBottomWidth = nScreenWidth - (2 * nPanelWidth)
        nBottomX = nPanelWidth
        
        oProjectPanel = getPanel("Project")
        if oProjectPanel != null and oProjectPanel.isVisible() {
            DrawRectangle(nBottomX, nBottomY, nBottomWidth, nBottomPanelHeight, oPanelColor)
            oProjectPanel.render(nBottomX, nBottomY, nBottomWidth, nBottomPanelHeight)
        }

    func renderStatusBar
        nScreenWidth = GetScreenWidth()
        nScreenHeight = GetScreenHeight()
        nY = nScreenHeight - nStatusBarHeight
        DrawRectangle(0, nY, nScreenWidth, nStatusBarHeight, oHeaderColor)
        oStatusBar.render(0, nY, nScreenWidth, nStatusBarHeight)

    func renderDialogs
        for oDialog in aDialogs {
            if oDialog.isVisible() {
                oDialog.render()
            }
        }

    func showDialog oDialog
        if oDialog != null {
            add(aDialogs, oDialog)
            oActiveDialog = oDialog
            oDialog.show()
        }

    func hideDialog oDialog
        if oDialog != null {
            nIndex = find(aDialogs, oDialog)
            if nIndex > 0 {
                del(aDialogs, nIndex)
            }
            oDialog.hide()
            
            if oActiveDialog = oDialog {
                oActiveDialog = null
            }
        }

    func setVisible bState
        bVisible = bState

    func isVisible
        return bVisible

    func getPanelWidth
        return nPanelWidth

    func getToolbarHeight
        return nToolbarHeight

    func getMenuBarHeight
        return nMenuBarHeight

    func getStatusBarHeight
        return nStatusBarHeight

    func getContentArea
        nScreenWidth = GetScreenWidth()
        nScreenHeight = GetScreenHeight()
        
        return [
            :x = nPanelWidth,
            :y = nMenuBarHeight + nToolbarHeight,
            :width = nScreenWidth - (2 * nPanelWidth),
            :height = nScreenHeight - nMenuBarHeight - nToolbarHeight - nStatusBarHeight - 200
        ]

    private
        aPanels
        aPanelMap
        oToolbar
        oMenuBar
        oStatusBar
        nPanelWidth
        nToolbarHeight
        nMenuBarHeight
        nStatusBarHeight
        oPanelColor
        oHeaderColor
        oTextColor
        oAccentColor
        bInitialized
        bVisible
        aDialogs
        oActiveDialog
}
