/*
الكلاس: Particle
الوصف: جسيمة واحدة في نظام الجسيمات
المدخلات: خصائص الجسيمة
المخرجات: جسيمة قابلة للتحديث والرسم
*/

class Particle {
    
    func init
        # الموقع والحركة
        aPosition = [0, 0, 0]
        aVelocity = [0, 0, 0]
        aAcceleration = [0, 0, 0]
        
        # الخصائص البصرية
        nSize = 1.0
        nStartSize = 1.0
        nEndSize = 0.1
        aColor = [1, 1, 1, 1]
        aStartColor = [1, 1, 1, 1]
        aEndColor = [1, 1, 1, 0]
        nRotation = 0.0
        nRotationSpeed = 0.0
        
        # العمر والحياة
        nLifetime = 2.0
        nLifeRemaining = 2.0
        bAlive = true
        
        # الخصائص الفيزيائية
        nMass = 1.0
        nDamping = 0.1
        nBounceStrength = 0.5
        nWindResistance = 1.0
        bGravityAffected = true
        bWindAffected = true
        bHasCollision = false
        
        # خصائص العرض
        bVisible = true
        oTexture = null
        nDistanceFromCamera = 0.0
        
        # معرف الجسيمة
        cID = generateParticleID()

    func generateParticleID
        return "PARTICLE_" + string(clock()) + "_" + string(random(99999))

    func update nDeltaTime
        if not bAlive {
            return
        }
        
        # تحديث العمر
        nLifeRemaining -= nDeltaTime
        if nLifeRemaining <= 0 {
            bAlive = false
            return
        }
        
        # تحديث الموقع
        aPosition[1] += aVelocity[1] * nDeltaTime
        aPosition[2] += aVelocity[2] * nDeltaTime
        aPosition[3] += aVelocity[3] * nDeltaTime
        
        # تحديث السرعة
        aVelocity[1] += aAcceleration[1] * nDeltaTime
        aVelocity[2] += aAcceleration[2] * nDeltaTime
        aVelocity[3] += aAcceleration[3] * nDeltaTime
        
        # تحديث الدوران
        nRotation += nRotationSpeed * nDeltaTime

    func updateLifetime nDeltaTime
        nLifeRemaining -= nDeltaTime
        if nLifeRemaining <= 0 {
            bAlive = false
        }

    func getLifePercent
        if nLifetime <= 0 {
            return 1.0
        }
        return 1.0 - (nLifeRemaining / nLifetime)

    func reset
        # إعادة تعيين الجسيمة للاستخدام مرة أخرى
        aPosition = [0, 0, 0]
        aVelocity = [0, 0, 0]
        aAcceleration = [0, 0, 0]
        nSize = 1.0
        aColor = [1, 1, 1, 1]
        nRotation = 0.0
        nLifeRemaining = nLifetime
        bAlive = true
        bVisible = true
        nDistanceFromCamera = 0.0

    func kill
        bAlive = false
        nLifeRemaining = 0

    func setPosition aPos
        aPosition = aPos

    func getPosition
        return aPosition

    func setVelocity aVel
        aVelocity = aVel

    func getVelocity
        return aVelocity

    func setAcceleration aAcc
        aAcceleration = aAcc

    func getAcceleration
        return aAcceleration

    func addForce aForce
        # إضافة قوة للجسيمة
        if nMass > 0 {
            aAcceleration[1] += aForce[1] / nMass
            aAcceleration[2] += aForce[2] / nMass
            aAcceleration[3] += aForce[3] / nMass
        }

    func setSize nNewSize
        nSize = nNewSize

    func getSize
        return nSize

    func setStartSize nNewSize
        nStartSize = nNewSize

    func getStartSize
        return nStartSize

    func setEndSize nNewSize
        nEndSize = nNewSize

    func getEndSize
        return nEndSize

    func setColor aNewColor
        aColor = aNewColor

    func getColor
        return aColor

    func setStartColor aNewColor
        aStartColor = aNewColor

    func getStartColor
        return aStartColor

    func setEndColor aNewColor
        aEndColor = aNewColor

    func getEndColor
        return aEndColor

    func setRotation nNewRotation
        nRotation = nNewRotation

    func getRotation
        return nRotation

    func setRotationSpeed nSpeed
        nRotationSpeed = nSpeed

    func getRotationSpeed
        return nRotationSpeed

    func setLifetime nNewLifetime
        nLifetime = nNewLifetime
        nLifeRemaining = nNewLifetime

    func getLifetime
        return nLifetime

    func getLifeRemaining
        return nLifeRemaining

    func setMass nNewMass
        nMass = nNewMass

    func getMass
        return nMass

    func setDamping nNewDamping
        nDamping = nNewDamping

    func getDamping
        return nDamping

    func setBounceStrength nStrength
        nBounceStrength = nStrength

    func getBounceStrength
        return nBounceStrength

    func setWindResistance nResistance
        nWindResistance = nResistance

    func getWindResistance
        return nWindResistance

    func setGravityAffected bState
        bGravityAffected = bState

    func isGravityAffected
        return bGravityAffected

    func setWindAffected bState
        bWindAffected = bState

    func isWindAffected
        return bWindAffected

    func setHasCollision bState
        bHasCollision = bState

    func hasCollision
        return bHasCollision

    func setVisible bState
        bVisible = bState

    func isVisible
        return bVisible

    func setTexture oTex
        oTexture = oTex

    func getTexture
        return oTexture

    func setDistanceFromCamera nDistance
        nDistanceFromCamera = nDistance

    func getDistanceFromCamera
        return nDistanceFromCamera

    func isAlive
        return bAlive

    func getID
        return cID

    private
        cID
        aPosition
        aVelocity
        aAcceleration
        nSize
        nStartSize
        nEndSize
        aColor
        aStartColor
        aEndColor
        nRotation
        nRotationSpeed
        nLifetime
        nLifeRemaining
        bAlive
        nMass
        nDamping
        nBounceStrength
        nWindResistance
        bGravityAffected
        bWindAffected
        bHasCollision
        bVisible
        oTexture
        nDistanceFromCamera
}
