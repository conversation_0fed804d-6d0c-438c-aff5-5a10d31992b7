/*
الكلاس: InputMapping
الوصف: ربط الأحداث بالمفاتيح وأزرار وحدة التحكم
*/

class InputMapping {
    
    func init cActionName, nKeyCode, nGamepadBtn = -1
        cAction = cActionName
        nKey = nKeyCode
        nGamepadButton = nGamepadBtn

    func getAction
        return cAction

    func getKey
        return nKey

    func setKey nKeyCode
        nKey = nKeyCode

    func getGamepadButton
        return nGamepadButton

    func setGamepadButton nGamepadBtn
        nGamepadButton = nGamepadBtn

    func hasGamepadButton
        return nGamepadButton != -1

    private
        cAction
        nKey
        nGamepadButton
}
