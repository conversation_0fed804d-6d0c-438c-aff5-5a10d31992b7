/*
الكلاس: ParticleSystem
الوصف: نظام جسيمات متقدم مع دعم أنواع مختلفة من التأثيرات
المدخلات: مولدات الجسيمات والإعدادات
المخرجات: تأثيرات جسيمات واقعية ومتنوعة
*/

load "raylib.ring"

class ParticleSystem {

    func init
        # قوائم الجسيمات والمولدات
        aParticles = []
        aEmitters = []
        aParticlePools = []  # مجمعات الجسيمات لتحسين الأداء

        # إعدادات النظام
        nMaxParticles = 10000
        nMaxEmitters = 50
        bEnabled = true

        # إحصائيات النظام
        nActiveParticles = 0
        nActiveEmitters = 0
        nParticlesCreated = 0
        nParticlesDestroyed = 0

        # إعدادات العرض
        bUseShader = false
        oShader = null
        oDefaultTexture = null

        # قوى عامة تؤثر على الجسيمات
        aGlobalForces = []
        aWindForce = [0, 0, 0]
        nGravityStrength = -9.81

        # تهيئة النظام
        initializeSystem()

    func initializeSystem
        # تهيئة مجمعات الجسيمات
        initializeParticlePools()

        # تحميل القوام الافتراضي
        loadDefaultTexture()

        # تحميل الشيدر إذا كان متوفراً
        loadParticleShader()

        ? "تم تهيئة نظام الجسيمات"

    func initializeParticlePools
        # إنشاء مجمعات جسيمات لأنواع مختلفة
        for i = 1 to 5 {  # 5 مجمعات مختلفة
            oPool = new ParticlePool(nMaxParticles / 5)
            add(aParticlePools, oPool)
        }

    func loadDefaultTexture
        # إنشاء قوام افتراضي للجسيمات
        try {
            oDefaultTexture = LoadTexture("assets/textures/particle_default.png")
            if not IsTextureReady(oDefaultTexture) {
                # إنشاء قوام بسيط إذا لم يكن الملف موجوداً
                oDefaultTexture = createDefaultParticleTexture()
            }
        catch
            oDefaultTexture = createDefaultParticleTexture()
        }

    func createDefaultParticleTexture
        # إنشاء قوام دائري بسيط
        nSize = 32
        oImage = GenImageColor(nSize, nSize, BLANK)

        # رسم دائرة
        for y = 0 to nSize-1 {
            for x = 0 to nSize-1 {
                nCenterX = nSize / 2
                nCenterY = nSize / 2
                nDistance = sqrt((x - nCenterX)^2 + (y - nCenterY)^2)
                nRadius = nSize / 2

                if nDistance <= nRadius {
                    nAlpha = 255 * (1.0 - nDistance / nRadius)
                    oColor = Color(255, 255, 255, nAlpha)
                    ImageDrawPixel(oImage, x, y, oColor)
                }
            }
        }

        oTexture = LoadTextureFromImage(oImage)
        UnloadImage(oImage)
        return oTexture

    func loadParticleShader
        # تحميل شيدر الجسيمات
        try {
            oShader = LoadShader("assets/shaders/particle.vert", "assets/shaders/particle.frag")
            if IsShaderReady(oShader) {
                bUseShader = true
                ? "تم تحميل شيدر الجسيمات"
            }
        catch
            ? "لم يتم العثور على شيدر الجسيمات، سيتم استخدام العرض الافتراضي"
        }

    func createEmitter cType = "default", aPos = [0, 0, 0]
        if len(aEmitters) >= nMaxEmitters {
            ? "تحذير: تم الوصول للحد الأقصى من مولدات الجسيمات"
            return null
        }

        oEmitter = new ParticleEmitter(cType, aPos)
        add(aEmitters, oEmitter)
        nActiveEmitters++

        return oEmitter

    func removeEmitter oEmitter
        nIndex = find(aEmitters, oEmitter)
        if nIndex > 0 {
            del(aEmitters, nIndex)
            nActiveEmitters--
            return true
        }
        return false

    func update nDeltaTime
        if not bEnabled {
            return
        }

        # تحديث مولدات الجسيمات
        updateEmitters(nDeltaTime)

        # تحديث الجسيمات الموجودة
        updateParticles(nDeltaTime)

        # إزالة الجسيمات الميتة
        removeDeadParticles()

        # تحديث الإحصائيات
        updateStatistics()

    func updateEmitters nDeltaTime
        for oEmitter in aEmitters {
            if oEmitter.isActive() {
                oEmitter.update(nDeltaTime)

                # إنشاء جسيمات جديدة
                nNewParticles = oEmitter.getParticlesPerFrame(nDeltaTime)
                for i = 1 to nNewParticles {
                    if nActiveParticles < nMaxParticles {
                        oParticle = createParticle(oEmitter)
                        if oParticle != null {
                            add(aParticles, oParticle)
                            nActiveParticles++
                            nParticlesCreated++
                        }
                    }
                }
            }
        }

    func createParticle oEmitter
        # إنشاء جسيمة جديدة من المولد
        oParticle = getParticleFromPool()
        if oParticle != null {
            oEmitter.initializeParticle(oParticle)
        }
        return oParticle

    func getParticleFromPool
        # الحصول على جسيمة من المجمع
        for oPool in aParticlePools {
            oParticle = oPool.getParticle()
            if oParticle != null {
                return oParticle
            }
        }

        # إنشاء جسيمة جديدة إذا لم تكن متوفرة في المجمع
        return new Particle()

    func updateParticles nDeltaTime
        for oParticle in aParticles {
            if oParticle.isAlive() {
                # تحديث الموقع
                updateParticlePosition(oParticle, nDeltaTime)

                # تحديث السرعة
                updateParticleVelocity(oParticle, nDeltaTime)

                # تحديث الخصائص البصرية
                updateParticleVisuals(oParticle, nDeltaTime)

                # تحديث العمر
                oParticle.updateLifetime(nDeltaTime)

                # فحص التصادمات إذا كان مفعلاً
                if oParticle.hasCollision() {
                    checkParticleCollisions(oParticle)
                }
            }
        }

    func updateParticlePosition oParticle, nDeltaTime
        # تحديث موقع الجسيمة
        aVelocity = oParticle.getVelocity()
        aPosition = oParticle.getPosition()

        aPosition[1] += aVelocity[1] * nDeltaTime
        aPosition[2] += aVelocity[2] * nDeltaTime
        aPosition[3] += aVelocity[3] * nDeltaTime

        oParticle.setPosition(aPosition)

    func updateParticleVelocity oParticle, nDeltaTime
        # تحديث سرعة الجسيمة
        aVelocity = oParticle.getVelocity()

        # تطبيق الجاذبية
        if oParticle.isGravityAffected() {
            aVelocity[2] += nGravityStrength * nDeltaTime
        }

        # تطبيق الرياح
        if oParticle.isWindAffected() {
            nWindStrength = oParticle.getWindResistance()
            aVelocity[1] += aWindForce[1] * nWindStrength * nDeltaTime
            aVelocity[2] += aWindForce[2] * nWindStrength * nDeltaTime
            aVelocity[3] += aWindForce[3] * nWindStrength * nDeltaTime
        }

        # تطبيق القوى العامة
        for aForce in aGlobalForces {
            aVelocity[1] += aForce[1] * nDeltaTime
            aVelocity[2] += aForce[2] * nDeltaTime
            aVelocity[3] += aForce[3] * nDeltaTime
        }

        # تطبيق المقاومة
        nDamping = oParticle.getDamping()
        aVelocity[1] *= (1.0 - nDamping * nDeltaTime)
        aVelocity[2] *= (1.0 - nDamping * nDeltaTime)
        aVelocity[3] *= (1.0 - nDamping * nDeltaTime)

        oParticle.setVelocity(aVelocity)

    func updateParticleVisuals oParticle, nDeltaTime
        # تحديث الخصائص البصرية للجسيمة
        nLifePercent = oParticle.getLifePercent()

        # تحديث الحجم
        nStartSize = oParticle.getStartSize()
        nEndSize = oParticle.getEndSize()
        nCurrentSize = lerp(nStartSize, nEndSize, nLifePercent)
        oParticle.setSize(nCurrentSize)

        # تحديث اللون
        aStartColor = oParticle.getStartColor()
        aEndColor = oParticle.getEndColor()
        aCurrentColor = lerpColor(aStartColor, aEndColor, nLifePercent)
        oParticle.setColor(aCurrentColor)

        # تحديث الدوران
        nRotationSpeed = oParticle.getRotationSpeed()
        nCurrentRotation = oParticle.getRotation()
        oParticle.setRotation(nCurrentRotation + nRotationSpeed * nDeltaTime)

    func checkParticleCollisions oParticle
        # فحص تصادمات الجسيمة مع البيئة
        aPosition = oParticle.getPosition()

        # فحص التصادم مع الأرض (مبسط)
        if aPosition[2] <= 0 {
            aVelocity = oParticle.getVelocity()
            nBounce = oParticle.getBounceStrength()

            if nBounce > 0 {
                # ارتداد الجسيمة
                aVelocity[2] = -aVelocity[2] * nBounce
                aPosition[2] = 0
                oParticle.setVelocity(aVelocity)
                oParticle.setPosition(aPosition)
            else
                # إيقاف الجسيمة
                oParticle.kill()
            }
        }

    func removeDeadParticles
        aNewParticles = []
        for oParticle in aParticles {
            if oParticle.isAlive() {
                add(aNewParticles, oParticle)
            else
                # إرجاع الجسيمة للمجمع
                returnParticleToPool(oParticle)
                nActiveParticles--
                nParticlesDestroyed++
            }
        }
        aParticles = aNewParticles

    func returnParticleToPool oParticle
        # إرجاع الجسيمة لمجمع مناسب
        for oPool in aParticlePools {
            if oPool.canAcceptParticle(oParticle) {
                oPool.returnParticle(oParticle)
                return
            }
        }

    func updateStatistics
        # تحديث إحصائيات النظام
        nActiveParticles = len(aParticles)
        nActiveEmitters = 0
        for oEmitter in aEmitters {
            if oEmitter.isActive() {
                nActiveEmitters++
            }
        }

    func render oCamera
        if not bEnabled or nActiveParticles = 0 {
            return
        }

        # ترتيب الجسيمات حسب المسافة من الكاميرا (للشفافية)
        sortParticlesByDistance(oCamera)

        # بداية رسم الجسيمات
        if bUseShader and oShader != null {
            BeginShaderMode(oShader)
        }

        # رسم كل الجسيمات
        for oParticle in aParticles {
            if oParticle.isVisible() {
                renderParticle(oParticle, oCamera)
            }
        }

        # انتهاء رسم الجسيمات
        if bUseShader and oShader != null {
            EndShaderMode()
        }

    func renderParticle oParticle, oCamera
        # رسم جسيمة واحدة
        aPosition = oParticle.getPosition()
        nSize = oParticle.getSize()
        aColor = oParticle.getColor()
        oTexture = oParticle.getTexture()

        if oTexture = null {
            oTexture = oDefaultTexture
        }

        # رسم الجسيمة كـ Billboard
        DrawBillboard(oCamera.getRayLibCamera(),
                     oTexture,
                     Vector3(aPosition[1], aPosition[2], aPosition[3]),
                     nSize,
                     Color(aColor[1]*255, aColor[2]*255, aColor[3]*255, aColor[4]*255))

    func sortParticlesByDistance oCamera
        # ترتيب الجسيمات حسب المسافة (مبسط)
        # يمكن تحسينه لاحقاً لأداء أفضل
        aCameraPos = oCamera.getPosition()

        # حساب المسافات
        for oParticle in aParticles {
            aParticlePos = oParticle.getPosition()
            nDistance = calculateDistance(aCameraPos, aParticlePos)
            oParticle.setDistanceFromCamera(nDistance)
        }

        # ترتيب بسيط (يمكن تحسينه)
        # هذا مبسط جداً ويحتاج خوارزمية ترتيب أفضل

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDy = aPos1[2] - aPos2[2]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

    func addGlobalForce aForce
        # إضافة قوة عامة تؤثر على جميع الجسيمات
        add(aGlobalForces, aForce)

    func removeGlobalForce aForce
        nIndex = find(aGlobalForces, aForce)
        if nIndex > 0 {
            del(aGlobalForces, nIndex)
        }

    func setWindForce aWind
        aWindForce = aWind

    func getWindForce
        return aWindForce

    func setGravityStrength nStrength
        nGravityStrength = nStrength

    func getGravityStrength
        return nGravityStrength

    func setEnabled bState
        bEnabled = bState

    func isEnabled
        return bEnabled

    func setMaxParticles nMax
        nMaxParticles = nMax

    func getMaxParticles
        return nMaxParticles

    func getActiveParticles
        return nActiveParticles

    func getActiveEmitters
        return nActiveEmitters

    func getParticlesCreated
        return nParticlesCreated

    func getParticlesDestroyed
        return nParticlesDestroyed

    func clearAllParticles
        # إزالة جميع الجسيمات
        for oParticle in aParticles {
            returnParticleToPool(oParticle)
        }
        aParticles = []
        nActiveParticles = 0

    func clearAllEmitters
        # إزالة جميع المولدات
        aEmitters = []
        nActiveEmitters = 0

    func lerp x, y, a
        return x * (1-a) + y * a

    func lerpColor c1, c2, a
        return [
            lerp(c1[1], c2[1], a),
            lerp(c1[2], c2[2], a),
            lerp(c1[3], c2[3], a),
            lerp(c1[4], c2[4], a)
        ]

    func cleanup
        # تنظيف موارد النظام
        clearAllParticles()
        clearAllEmitters()

        # تنظيف المجمعات
        for oPool in aParticlePools {
            oPool.cleanup()
        }
        aParticlePools = []

        # تنظيف الموارد
        if oShader != null {
            UnloadShader(oShader)
        }
        if oDefaultTexture != null {
            UnloadTexture(oDefaultTexture)
        }

        ? "تم تنظيف نظام الجسيمات"

    private
        aParticles
        aEmitters
        aParticlePools
        aGlobalForces
        aWindForce
        oShader
        oDefaultTexture
        nMaxParticles
        nMaxEmitters
        nActiveParticles
        nActiveEmitters
        nParticlesCreated
        nParticlesDestroyed
        nGravityStrength
        bEnabled
        bUseShader
}
