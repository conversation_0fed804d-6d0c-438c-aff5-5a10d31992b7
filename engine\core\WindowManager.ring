class WindowManager {
   

    func init
        InitWindow(nWidth, nHeight, cTitle)
        SetTargetFPS(nTargetFPS)
        initCamera()

    func initCamera
        oCamera = new Camera3D(
            [0, 10, 10],    # الموقع
            [0, 0, 0],      # الهدف
            [0, 1, 0],      # الاتجاه العلوي
            45,             # زاوية الرؤية
            CAMERA_PERSPECTIVE
        )

    func getCamera
        return oCamera

    func setResolution nW, nH
        nWidth = nW
        nHeight = nH
        SetWindowSize(nWidth, nHeight)

    func toggleFullscreen
        ToggleFullscreen()

    func cleanup
        # تنظيف موارد النافذة
    
     private
        nWidth = 1280
        nHeight = 720
        cTitle = "محرك الألعاب الاحترافي"
        nTargetFPS = 60
        oCamera
}
