/*
الكلاس: SceneEditor
الوصف: محرر مشاهد متقدم مع واجهة مستخدم شاملة
المدخلات: مشاهد وكائنات اللعبة
المخرجات: محرر مرئي تفاعلي
*/

load "raylib.ring"
load "../core/Scene.ring"
load "../core/GameObject.ring"
load "../core/Camera.ring"

# أنماط التحرير
EDIT_MODE_SELECT = 0
EDIT_MODE_MOVE = 1
EDIT_MODE_ROTATE = 2
EDIT_MODE_SCALE = 3

# أنماط العرض
VIEW_MODE_SOLID = 0
VIEW_MODE_WIREFRAME = 1
VIEW_MODE_TEXTURED = 2

class SceneEditor {

    func init
        # إعداد المحرر
        cEditorName = "Scene Editor"
        cVersion = "1.0"
        bInitialized = false

        # حالة المحرر
        nEditMode = EDIT_MODE_SELECT
        nViewMode = VIEW_MODE_SOLID
        bGridEnabled = true
        bSnapToGrid = true
        bShowGizmos = true
        bShowStats = true

        # إعدادات الشبكة
        nGridSize = 1.0
        nGridLines = 20
        oGridColor = GRAY

        # الكائنات المحددة
        oSelectedObject = null
        aSelectedObjects = []
        bMultiSelect = false

        # أدوات التحرير
        oGizmo = null
        oCamera = null
        oUI = null

        # إدارة المشاهد
        oCurrentScene = null
        cCurrentScenePath = ""
        bSceneModified = false

        # نظام التراجع/الإعادة
        aUndoStack = []
        aRedoStack = []
        nMaxUndoSteps = 50

        # إعدادات الكاميرا
        aCameraPosition = [10, 10, 10]
        aCameraTarget = [0, 0, 0]
        nCameraSpeed = 5.0
        nCameraRotationSpeed = 2.0

        # إحصائيات المحرر
        nObjectCount = 0
        nTriangleCount = 0
        nDrawCalls = 0

        initializeEditor()

    func initializeEditor
        ? "تهيئة محرر المشاهد..."

        # تهيئة الكاميرا
        initializeCamera()

        # تهيئة واجهة المستخدم
        initializeUI()

        # تهيئة أدوات التحرير
        initializeTools()

        # تهيئة المشهد الافتراضي
        createDefaultScene()

        bInitialized = true
        ? "تم تهيئة محرر المشاهد بنجاح"

    func initializeCamera
        oCamera = new Camera(aCameraPosition, aCameraTarget, [0, 1, 0])
        oCamera.setControlMode(CAMERA_CONTROL_FREE)
        oCamera.setMovementSpeed(nCameraSpeed)
        oCamera.setMouseSensitivity(0.003)

    func initializeUI
        oUI = new EditorUI()
        oUI.initialize()

        # إنشاء اللوحات
        oHierarchyPanel = new HierarchyPanel()
        oInspectorPanel = new InspectorPanel()
        oProjectPanel = new ProjectPanel()
        oConsolePanel = new ConsolePanel()
        oToolbar = new EditorToolbar()
        oMenuBar = new EditorMenuBar()

        # إضافة اللوحات للواجهة
        oUI.addPanel("Hierarchy", oHierarchyPanel)
        oUI.addPanel("Inspector", oInspectorPanel)
        oUI.addPanel("Project", oProjectPanel)
        oUI.addPanel("Console", oConsolePanel)
        oUI.setToolbar(oToolbar)
        oUI.setMenuBar(oMenuBar)

    func initializeTools
        oGizmo = new TransformGizmo()
        oGizmo.setMode(nEditMode)

    func createDefaultScene
        oCurrentScene = new Scene("New Scene")

        # إضافة كائنات افتراضية
        oGround = new GameObject("Ground")
        oGround.setPosition([0, 0, 0])
        oGround.setScale([20, 1, 20])
        oCurrentScene.addGameObject(oGround)

        # إضافة إضاءة افتراضية
        oLight = new Light3D([0, 10, 0], WHITE, LIGHT_DIRECTIONAL)
        oCurrentScene.addLight(oLight)

        updateHierarchy()

    func update nDeltaTime
        if not bInitialized {
            return
        }

        # تحديث الكاميرا
        updateCamera(nDeltaTime)

        # معالجة المدخلات
        handleInput()

        # تحديث أدوات التحرير
        updateTools(nDeltaTime)

        # تحديث واجهة المستخدم
        updateUI(nDeltaTime)

        # تحديث المشهد
        if oCurrentScene != null {
            oCurrentScene.update(nDeltaTime)
        }

        # تحديث الإحصائيات
        updateStatistics()

    func updateCamera nDeltaTime
        if oCamera != null {
            # تحديث الكاميرا مع مدخلات المحرر
            oCamera.update(nDeltaTime, getInputManager())
        }

    func updateTools nDeltaTime
        if oGizmo != null and oSelectedObject != null {
            oGizmo.update(nDeltaTime)
        }

    func updateUI nDeltaTime
        if oUI != null {
            oUI.update(nDeltaTime)

            # تحديث اللوحات
            updatePanels()
        }

    func updatePanels
        # تحديث لوحة التسلسل الهرمي
        if oHierarchyPanel != null {
            oHierarchyPanel.setScene(oCurrentScene)
            oHierarchyPanel.setSelectedObject(oSelectedObject)
        }

        # تحديث لوحة المفتش
        if oInspectorPanel != null {
            oInspectorPanel.setSelectedObject(oSelectedObject)
        }

        # تحديث لوحة المشروع
        if oProjectPanel != null {
            oProjectPanel.refresh()
        }

    func updateStatistics
        # تحديث إحصائيات المحرر
        if oCurrentScene != null {
            nObjectCount = oCurrentScene.getObjectCount()
            nTriangleCount = oCurrentScene.getTriangleCount()
        }

    func render
        if not bInitialized {
            return
        }

        BeginDrawing()
            ClearBackground(DARKGRAY)

            # رسم المشهد ثلاثي الأبعاد
            render3DView()

            # رسم واجهة المستخدم
            renderUI()

            # رسم معلومات التصحيح
            if bShowStats {
                renderDebugInfo()
            }

        EndDrawing()

    func render3DView
        if oCamera = null {
            return
        }

        BeginMode3D(oCamera.getRayLibCamera())
            # رسم الشبكة
            if bGridEnabled {
                renderGrid()
            }

            # رسم المشهد
            renderScene()

            # رسم أدوات التحرير
            renderEditorTools()

        EndMode3D()

    func renderGrid
        # رسم شبكة الأرضية
        DrawGrid(nGridLines, nGridSize)

        # رسم المحاور
        DrawLine3D([0, 0, 0], [5, 0, 0], RED)    # X axis
        DrawLine3D([0, 0, 0], [0, 5, 0], GREEN)  # Y axis
        DrawLine3D([0, 0, 0], [0, 0, 5], BLUE)   # Z axis

    func renderScene
        if oCurrentScene = null {
            return
        }

        # رسم جميع كائنات المشهد
        aGameObjects = oCurrentScene.getGameObjects()
        for oObject in aGameObjects {
            renderGameObject(oObject)
        }

        # رسم الأضواء (للتصحيح)
        aLights = oCurrentScene.getLights()
        for oLight in aLights {
            if bShowGizmos {
                oLight.render()
            }
        }

    func renderGameObject oObject
        if oObject = null or not oObject.isVisible() {
            return
        }

        # رسم الكائن
        oObject.render()

        # رسم حدود الكائن إذا كان محدداً
        if oObject = oSelectedObject {
            renderSelectionOutline(oObject)
        }

    func renderSelectionOutline oObject
        # رسم إطار حول الكائن المحدد
        aBounds = oObject.getBounds()
        if aBounds != null {
            DrawCubeWires(aBounds[:center], aBounds[:size], YELLOW)
        }

    func renderEditorTools
        # رسم أدوات التحرير
        if oGizmo != null and oSelectedObject != null and bShowGizmos {
            oGizmo.render(oSelectedObject)
        }

    func renderUI
        if oUI != null {
            oUI.render()
        }

    func renderDebugInfo
        # رسم معلومات التصحيح
        nY = 10
        DrawText("Objects: " + string(nObjectCount), 10, nY, 20, WHITE)
        nY += 25
        DrawText("Triangles: " + string(nTriangleCount), 10, nY, 20, WHITE)
        nY += 25
        DrawText("Draw Calls: " + string(nDrawCalls), 10, nY, 20, WHITE)
        nY += 25

        if oSelectedObject != null {
            DrawText("Selected: " + oSelectedObject.getName(), 10, nY, 20, YELLOW)
            nY += 25
        }

        DrawText("Mode: " + getEditModeString(), 10, nY, 20, GREEN)

    func handleInput
        # معالجة مدخلات المحرر
        handleKeyboardInput()
        handleMouseInput()

    func handleKeyboardInput
        # اختصارات لوحة المفاتيح
        if IsKeyDown(KEY_LEFT_CONTROL) {
            if IsKeyPressed(KEY_Z) {
                undo()
            }
            if IsKeyPressed(KEY_Y) {
                redo()
            }
            if IsKeyPressed(KEY_S) {
                saveScene()
            }
            if IsKeyPressed(KEY_O) {
                openScene()
            }
            if IsKeyPressed(KEY_N) {
                newScene()
            }
            if IsKeyPressed(KEY_D) {
                duplicateSelected()
            }
        }

        # مفاتيح الحذف
        if IsKeyPressed(KEY_DELETE) and oSelectedObject != null {
            deleteSelectedObject()
        }

        # تغيير أنماط التحرير
        if IsKeyPressed(KEY_Q) {
            setEditMode(EDIT_MODE_SELECT)
        }
        if IsKeyPressed(KEY_W) {
            setEditMode(EDIT_MODE_MOVE)
        }
        if IsKeyPressed(KEY_E) {
            setEditMode(EDIT_MODE_ROTATE)
        }
        if IsKeyPressed(KEY_R) {
            setEditMode(EDIT_MODE_SCALE)
        }

        # تبديل الإعدادات
        if IsKeyPressed(KEY_G) {
            toggleGrid()
        }
        if IsKeyPressed(KEY_F) {
            focusOnSelected()
        }

    func handleMouseInput
        # معالجة مدخلات الفأرة
        if IsMouseButtonPressed(MOUSE_LEFT_BUTTON) {
            handleSelection()
        }

        if IsMouseButtonPressed(MOUSE_RIGHT_BUTTON) {
            showContextMenu()
        }

        # التحكم في الكاميرا بالفأرة الوسطى
        if IsMouseButtonDown(MOUSE_MIDDLE_BUTTON) {
            handleCameraControl()
        }

        # التكبير بالعجلة
        nWheelMove = GetMouseWheelMove()
        if nWheelMove != 0 {
            zoomCamera(nWheelMove)
        }

    func handleSelection
        # معالجة تحديد الكائنات
        aMousePos = GetMousePosition()
        oRay = GetMouseRay(aMousePos, oCamera.getRayLibCamera())

        # البحث عن كائن تحت المؤشر
        oHitObject = performRaycast(oRay)

        # التحديد المتعدد مع Ctrl
        if IsKeyDown(KEY_LEFT_CONTROL) {
            if oHitObject != null {
                toggleObjectSelection(oHitObject)
            }
        else
            # تحديد واحد
            selectObject(oHitObject)
        }

        # تحديث واجهة المستخدم
        updateSelectionUI()

    func selectObject oObject
        # إلغاء التحديد السابق
        clearSelection()

        # تحديد الكائن الجديد
        oSelectedObject = oObject
        if oObject != null {
            add(aSelectedObjects, oObject)
            ? "تم تحديد: " + oObject.getName()
        }

    func toggleObjectSelection oObject
        # تبديل تحديد الكائن
        if oObject = null {
            return
        }

        nIndex = find(aSelectedObjects, oObject)
        if nIndex > 0 {
            # إلغاء التحديد
            del(aSelectedObjects, nIndex)
            if oSelectedObject = oObject {
                if len(aSelectedObjects) > 0 {
                    oSelectedObject = aSelectedObjects[1]
                else
                    oSelectedObject = null
                }
            }
        else
            # إضافة للتحديد
            add(aSelectedObjects, oObject)
            oSelectedObject = oObject
        }

    func clearSelection
        # إلغاء جميع التحديدات
        aSelectedObjects = []
        oSelectedObject = null

    func updateSelectionUI
        # تحديث واجهة المستخدم للتحديد
        if oInspectorPanel != null {
            oInspectorPanel.setSelectedObject(oSelectedObject)
        }

        if oHierarchyPanel != null {
            oHierarchyPanel.setSelectedObjects(aSelectedObjects)
        }

    func performRaycast oRay
        # تنفيذ عملية الريكاست للعثور على كائن
        if oCurrentScene = null {
            return null
        }

        aGameObjects = oCurrentScene.getGameObjects()
        nClosestDistance = 999999
        oClosestObject = null

        for oObject in aGameObjects {
            if oObject.isVisible() {
                # فحص التصادم مع الكائن
                nDistance = checkRayObjectIntersection(oRay, oObject)
                if nDistance > 0 and nDistance < nClosestDistance {
                    nClosestDistance = nDistance
                    oClosestObject = oObject
                }
            }
        }

        return oClosestObject

    func checkRayObjectIntersection oRay, oObject
        # فحص تصادم الشعاع مع كائن
        # هذا تنفيذ مبسط - يحتاج تطوير أكثر
        aBounds = oObject.getBounds()
        if aBounds != null {
            # فحص تصادم مع صندوق الحدود
            return checkRayBoxIntersection(oRay, aBounds)
        }
        return -1

    func checkRayBoxIntersection oRay, aBounds
        # فحص تصادم الشعاع مع صندوق
        # تنفيذ مبسط - يحتاج خوارزمية أكثر دقة
        aCenter = aBounds[:center]
        aSize = aBounds[:size]

        # حساب المسافة التقريبية
        aRayPos = oRay.position
        nDistance = sqrt(
            (aRayPos.x - aCenter[1])^2 +
            (aRayPos.y - aCenter[2])^2 +
            (aRayPos.z - aCenter[3])^2
        )

        # فحص بسيط للتصادم
        if nDistance <= (aSize[1] + aSize[2] + aSize[3]) / 3 {
            return nDistance
        }

        return -1

    func addGameObject cType
        oNewObject = new GameObject(cType)
        if bSnapToGrid {
            snapToGrid(oNewObject)
        }
        
        if oCurrentScene {
            oCurrentScene.addObject(oNewObject)
            addToUndoStack("Add " + cType)
        }

        return oNewObject

    func deleteSelectedObject
        if oSelectedObject {
            addToUndoStack("Delete " + oSelectedObject.getName())
            oCurrentScene.removeObject(oSelectedObject)
            oSelectedObject = null
        }

    func saveScene
        if oCurrentScene {
            oCurrentScene.save()
        }

    func loadScene cPath
        oCurrentScene = new Scene
        oCurrentScene.load(cPath)

    func undo
        if len(aUndoStack) > 0 {
            oAction = aUndoStack[len(aUndoStack)]
            del(aUndoStack, len(aUndoStack))
            add(aRedoStack, oAction)
            oAction.undo()
        }

    func redo
        if len(aRedoStack) > 0 {
            oAction = aRedoStack[len(aRedoStack)]
            del(aRedoStack, len(aRedoStack))
            add(aUndoStack, oAction)
            oAction.redo()
        }

    private
        oCurrentScene
        oSelectedObject
        oGizmo
        oCamera
        oUI
        bGridEnabled = true
        nGridSize = 1.0
        bSnapToGrid = true
        aUndoStack = []
        aRedoStack = []

    func addToUndoStack cDescription
        oAction = new EditorAction(cDescription)
        add(aUndoStack, oAction)
        aRedoStack = [] # مسح ستاك الإعادة عند إضافة إجراء جديد

    func snapToGrid oObject
        aPos = oObject.getPosition()
        for i = 1 to 3 {
            aPos[i] = round(aPos[i] / nGridSize) * nGridSize
        }
        oObject.setPosition(aPos)

    func performRaycast oRay
        # تنفيذ عملية الريكاست وإرجاع الكائن المحدد
}
