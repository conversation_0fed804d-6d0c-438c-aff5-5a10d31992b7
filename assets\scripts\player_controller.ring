/*
نص برمجي: PlayerController
الوصف: تحكم في حركة اللاعب والتفاعل مع البيئة
المدخلات: مدخلات المستخدم
المخرجات: حركة وتفاعل اللاعب
*/

load "../../engine/core/Component.ring"

class PlayerController from Component {
    
    func init
        super.init("PlayerController")
        
        # إعدادات الحركة
        nMovementSpeed = 5.0
        nRunMultiplier = 2.0
        nJumpHeight = 2.0
        nRotationSpeed = 180.0
        
        # حالة اللاعب
        bIsGrounded = true
        bIsRunning = false
        bIsJumping = false
        aVelocity = [0, 0, 0]
        
        # إعدادات الفيزياء
        nGravity = -9.81
        nGroundCheckDistance = 0.1
        
        # مراجع المكونات
        oInputManager = null
        oPhysicsBody = null
        oAnimator = null
        
        # إحصائيات
        nDistanceTraveled = 0.0
        nJumpCount = 0
        nLastGroundTime = 0.0

    func onAttach
        # الحصول على مراجع المكونات المطلوبة
        oInputManager = getEngine().getInputManager()
        oPhysicsBody = getOwner().getComponent("RigidBody")
        oAnimator = getOwner().getComponent("Animator")
        
        # إعداد مدخلات اللاعب
        setupInputMappings()

    func setupInputMappings
        # إعداد خرائط المدخلات
        if oInputManager != null {
            oInputManager.addInputMapping("MoveForward", KEY_W)
            oInputManager.addInputMapping("MoveBackward", KEY_S)
            oInputManager.addInputMapping("MoveLeft", KEY_A)
            oInputManager.addInputMapping("MoveRight", KEY_D)
            oInputManager.addInputMapping("Jump", KEY_SPACE)
            oInputManager.addInputMapping("Run", KEY_LEFT_SHIFT)
            oInputManager.addInputMapping("Interact", KEY_E)
        }

    func update nDeltaTime
        if not isActive() or oInputManager = null {
            return
        }
        
        # فحص الأرضية
        checkGrounded()
        
        # معالجة المدخلات
        handleMovementInput(nDeltaTime)
        handleJumpInput()
        handleInteractionInput()
        
        # تطبيق الفيزياء
        applyPhysics(nDeltaTime)
        
        # تحديث الرسوم المتحركة
        updateAnimations()
        
        # تحديث الإحصائيات
        updateStatistics(nDeltaTime)

    func handleMovementInput nDeltaTime
        # الحصول على اتجاه الحركة
        aMovementInput = getMovementInput()
        
        # فحص الجري
        bIsRunning = oInputManager.isActionDown("Run")
        
        # حساب السرعة
        nCurrentSpeed = nMovementSpeed
        if bIsRunning {
            nCurrentSpeed *= nRunMultiplier
        }
        
        # تطبيق الحركة
        if aMovementInput[1] != 0 or aMovementInput[3] != 0 {
            movePlayer(aMovementInput, nCurrentSpeed, nDeltaTime)
        }

    func getMovementInput
        aInput = [0, 0, 0]
        
        if oInputManager.isActionDown("MoveForward") {
            aInput[3] += 1
        }
        if oInputManager.isActionDown("MoveBackward") {
            aInput[3] -= 1
        }
        if oInputManager.isActionDown("MoveLeft") {
            aInput[1] -= 1
        }
        if oInputManager.isActionDown("MoveRight") {
            aInput[1] += 1
        }
        
        # تطبيع الاتجاه
        nLength = sqrt(aInput[1]*aInput[1] + aInput[3]*aInput[3])
        if nLength > 0 {
            aInput[1] /= nLength
            aInput[3] /= nLength
        }
        
        return aInput

    func movePlayer aDirection, nSpeed, nDeltaTime
        if getOwner() = null {
            return
        }
        
        # الحصول على موقع اللاعب الحالي
        aCurrentPos = getOwner().getPosition()
        
        # حساب الموقع الجديد
        aNewPos = [
            aCurrentPos[1] + aDirection[1] * nSpeed * nDeltaTime,
            aCurrentPos[2],  # لا نغير Y هنا
            aCurrentPos[3] + aDirection[3] * nSpeed * nDeltaTime
        ]
        
        # تطبيق الموقع الجديد
        getOwner().setPosition(aNewPos)
        
        # دوران اللاعب في اتجاه الحركة
        if aDirection[1] != 0 or aDirection[3] != 0 {
            rotateTowardsDirection(aDirection, nDeltaTime)
        }

    func rotateTowardsDirection aDirection, nDeltaTime
        # حساب الزاوية المطلوبة
        nTargetAngle = atan2(aDirection[1], aDirection[3]) * 180.0 / 3.14159
        
        # الحصول على الدوران الحالي
        aCurrentRotation = getOwner().getRotation()
        nCurrentAngle = aCurrentRotation[2]  # Y rotation
        
        # حساب الفرق في الزاوية
        nAngleDiff = nTargetAngle - nCurrentAngle
        
        # تطبيع الزاوية
        while nAngleDiff > 180 {
            nAngleDiff -= 360
        }
        while nAngleDiff < -180 {
            nAngleDiff += 360
        }
        
        # تطبيق الدوران التدريجي
        nRotationStep = nRotationSpeed * nDeltaTime
        if abs(nAngleDiff) < nRotationStep {
            nCurrentAngle = nTargetAngle
        else
            if nAngleDiff > 0 {
                nCurrentAngle += nRotationStep
            else
                nCurrentAngle -= nRotationStep
            }
        }
        
        # تطبيق الدوران الجديد
        aNewRotation = [aCurrentRotation[1], aCurrentRotation[2], nCurrentAngle]
        getOwner().setRotation(aNewRotation)

    func handleJumpInput
        if oInputManager.isActionPressed("Jump") and bIsGrounded {
            jump()
        }

    func jump
        if not bIsGrounded {
            return
        }
        
        # تطبيق قوة القفز
        aVelocity[2] = sqrt(2.0 * abs(nGravity) * nJumpHeight)
        bIsJumping = true
        bIsGrounded = false
        nJumpCount++
        
        # تطبيق السرعة على الجسم الفيزيائي
        if oPhysicsBody != null {
            oPhysicsBody.setVelocity(aVelocity)
        }

    func handleInteractionInput
        if oInputManager.isActionPressed("Interact") {
            # البحث عن كائنات قابلة للتفاعل
            checkForInteractables()
        }

    func checkForInteractables
        # البحث عن كائنات قابلة للتفاعل في المنطقة المحيطة
        aPlayerPos = getOwner().getPosition()
        nInteractionRange = 2.0
        
        # هذا يتطلب تكامل مع نظام الفيزياء للبحث عن الكائنات
        ? "البحث عن كائنات قابلة للتفاعل..."

    func checkGrounded
        # فحص ما إذا كان اللاعب على الأرض
        aPlayerPos = getOwner().getPosition()
        
        # إجراء فحص بسيط للأرضية
        # هذا يتطلب تكامل مع نظام الفيزياء
        if aPlayerPos[2] <= 0.5 {  # فحص مبسط
            if not bIsGrounded {
                onLanded()
            }
            bIsGrounded = true
            nLastGroundTime = GetTime()
        else
            bIsGrounded = false
        }

    func onLanded
        # يتم استدعاؤها عند الهبوط على الأرض
        bIsJumping = false
        aVelocity[2] = 0

    func applyPhysics nDeltaTime
        # تطبيق الجاذبية
        if not bIsGrounded {
            aVelocity[2] += nGravity * nDeltaTime
            
            # تطبيق السرعة العمودية
            aCurrentPos = getOwner().getPosition()
            aNewPos = [
                aCurrentPos[1],
                aCurrentPos[2] + aVelocity[2] * nDeltaTime,
                aCurrentPos[3]
            ]
            getOwner().setPosition(aNewPos)
        }

    func updateAnimations
        # تحديث الرسوم المتحركة
        if oAnimator = null {
            return
        }
        
        # تحديد الرسم المتحرك المناسب
        if not bIsGrounded {
            if bIsJumping {
                oAnimator.playAnimation("jump")
            else
                oAnimator.playAnimation("fall")
            }
        elseif aVelocity[1] != 0 or aVelocity[3] != 0
            if bIsRunning {
                oAnimator.playAnimation("run")
            else
                oAnimator.playAnimation("walk")
            }
        else
            oAnimator.playAnimation("idle")
        }

    func updateStatistics nDeltaTime
        # تحديث إحصائيات اللاعب
        if aVelocity[1] != 0 or aVelocity[3] != 0 {
            nSpeed = sqrt(aVelocity[1]*aVelocity[1] + aVelocity[3]*aVelocity[3])
            nDistanceTraveled += nSpeed * nDeltaTime
        }

    func getMovementSpeed
        return nMovementSpeed

    func setMovementSpeed nSpeed
        nMovementSpeed = nSpeed

    func getJumpHeight
        return nJumpHeight

    func setJumpHeight nHeight
        nJumpHeight = nHeight

    func isGrounded
        return bIsGrounded

    func isRunning
        return bIsRunning

    func getDistanceTraveled
        return nDistanceTraveled

    func getJumpCount
        return nJumpCount

    func getEngine
        # الحصول على مرجع المحرك
        # هذا يتطلب تنفيذ في النظام الأساسي
        return null

    private
        nMovementSpeed
        nRunMultiplier
        nJumpHeight
        nRotationSpeed
        bIsGrounded
        bIsRunning
        bIsJumping
        aVelocity
        nGravity
        nGroundCheckDistance
        oInputManager
        oPhysicsBody
        oAnimator
        nDistanceTraveled
        nJumpCount
        nLastGroundTime
}
