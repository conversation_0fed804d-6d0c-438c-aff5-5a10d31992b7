/*
سكريبت التثبيت السريع لمحرك الألعاب Ring Game Engine
يقوم بفحص المتطلبات وإعداد البيئة
*/

? "==================================================="
? "    محرك الألعاب Ring Game Engine"
? "    سكريبت التثبيت والإعداد"
? "==================================================="
? ""

# فحص إصدار Ring
? "فحص إصدار Ring..."
cRingVersion = version()
? "إصدار Ring المثبت: " + cRingVersion

# فحص المتطلبات الأساسية
? ""
? "فحص المتطلبات..."

# فحص وجود RayLib
try {
    load "raylib.ring"
    ? "✓ RayLib متوفرة"
    bRayLibOK = true
catch
    ? "✗ RayLib غير متوفرة - يرجى تثبيتها"
    bRayLibOK = false
}

# فحص دعم OpenGL
? "فحص دعم OpenGL..."
try {
    # محاولة إنشاء نافذة صغيرة للاختبار
    InitWindow(100, 100, "Test")
    if IsWindowReady() {
        ? "✓ OpenGL متوفر"
        bOpenGLOK = true
        CloseWindow()
    else
        ? "✗ فشل في تهيئة OpenGL"
        bOpenGLOK = false
    }
catch
    ? "✗ خطأ في فحص OpenGL"
    bOpenGLOK = false
}

# فحص مجلدات المشروع
? ""
? "فحص هيكل المشروع..."

aRequiredDirs = [
    "engine",
    "engine/core",
    "engine/graphics",
    "engine/physics", 
    "engine/audio",
    "engine/ai",
    "engine/particles",
    "engine/input",
    "engine/editor",
    "assets",
    "assets/models",
    "assets/textures",
    "assets/sounds",
    "assets/music",
    "assets/scenes",
    "examples",
    "docs"
]

for cDir in aRequiredDirs {
    if isdir(cDir) {
        ? "✓ " + cDir
    else
        ? "✗ " + cDir + " - مفقود"
        mkdir(cDir)
        ? "  تم إنشاء المجلد"
    }
}

# فحص الملفات الأساسية
? ""
? "فحص الملفات الأساسية..."

aRequiredFiles = [
    "game_engine.ring",
    "engine/core/Engine.ring",
    "engine/core/GameObject.ring",
    "engine/core/Component.ring",
    "engine/core/Scene.ring",
    "docs/README.md"
]

for cFile in aRequiredFiles {
    if fexists(cFile) {
        ? "✓ " + cFile
    else
        ? "✗ " + cFile + " - مفقود"
    }
}

# إنشاء ملف تكوين
? ""
? "إنشاء ملف التكوين..."

cConfigContent = '
# ملف تكوين محرك الألعاب
[Engine]
Name=Ring Game Engine
Version=1.0.0
Author=Ring Community

[Graphics]
DefaultWidth=1280
DefaultHeight=720
VSync=true
MSAA=4
MaxFPS=60

[Audio]
MasterVolume=1.0
MusicVolume=0.8
SoundVolume=1.0
MaxSources=32

[Physics]
Gravity=-9.81
TimeStep=0.016667
MaxSubSteps=10

[AI]
UpdateFrequency=30
MaxAgents=100

[Particles]
MaxParticles=10000
MaxEmitters=50

[Input]
MouseSensitivity=1.0
GamepadDeadzone=0.1

[Debug]
ShowStats=false
ShowBounds=false
ShowNormals=false
LogLevel=INFO
'

write("config.ini", cConfigContent)
? "✓ تم إنشاء ملف التكوين"

# إنشاء مثال بسيط للاختبار
? ""
? "إنشاء مثال للاختبار..."

cTestExample = '
# مثال اختبار بسيط
load "game_engine.ring"

? "اختبار محرك الألعاب..."

try {
    # إنشاء محرك بسيط
    oEngine = new Engine(800, 600, "اختبار المحرك")
    
    # إنشاء مشهد بسيط
    oScene = new Scene("TestScene")
    
    # إنشاء كائن مكعب
    oCube = new GameObject("TestCube")
    oCube.setPosition([0, 0, 0])
    
    oScene.addGameObject(oCube)
    oEngine.getSceneManager().setCurrentScene(oScene)
    
    ? "✓ تم إنشاء المحرك بنجاح"
    ? "✓ تم إنشاء المشهد بنجاح"
    ? "✓ تم إنشاء الكائن بنجاح"
    
    ? ""
    ? "المحرك جاهز للاستخدام!"
    ? "شغل: ring examples/simple_game/simple_game.ring"
    
catch
    ? "✗ خطأ في اختبار المحرك: " + cCatchError
}
'

write("test_engine.ring", cTestExample)
? "✓ تم إنشاء مثال الاختبار"

# تقرير النتائج النهائية
? ""
? "==================================================="
? "                تقرير التثبيت"
? "==================================================="

if bRayLibOK and bOpenGLOK {
    ? "✓ التثبيت مكتمل بنجاح!"
    ? ""
    ? "الخطوات التالية:"
    ? "1. شغل: ring test_engine.ring للاختبار"
    ? "2. اطلع على الأمثلة في مجلد examples/"
    ? "3. اقرأ دليل المطور في docs/developer_guide.md"
    ? "4. ابدأ في تطوير لعبتك الأولى!"
else
    ? "✗ التثبيت غير مكتمل"
    ? ""
    ? "المشاكل المكتشفة:"
    if not bRayLibOK {
        ? "- RayLib غير متوفرة"
        ? "  الحل: ثبت Ring مع RayLib"
    }
    if not bOpenGLOK {
        ? "- OpenGL غير متوفر"
        ? "  الحل: حدث تعريفات كرت الرسوميات"
    }
}

? ""
? "للمساعدة:"
? "- دليل المطور: docs/developer_guide.md"
? "- الأسئلة الشائعة: docs/faq.md"
? "- منتدى Ring: https://groups.google.com/forum/#!forum/ring-lang"
? ""
? "==================================================="

# دالة مساعدة لفحص وجود مجلد
func isdir cPath
    try {
        dir(cPath)
        return true
    catch
        return false
    }

# دالة مساعدة لإنشاء مجلد
func mkdir cPath
    try {
        system("mkdir " + cPath)
        return true
    catch
        return false
    }
