/*
مثال: لعبة بسيطة
الوصف: مثال شامل يوضح استخدام محرك الألعاب
المدخلات: مدخلات اللاعب
المخرجات: لعبة تفاعلية بسيطة
*/

# تحميل المحرك
load "../../game_engine.ring"

# الكلاس الرئيسي للعبة
class SimpleGame {
    
    func init
        # إنشاء المحرك
        oEngine = new Engine(1280, 720, "لعبة بسيطة - Ring Game Engine")
        
        # متغيرات اللعبة
        oPlayer = null
        aEnemies = []
        aCollectibles = []
        nScore = 0
        nLevel = 1
        bGameRunning = false
        
        # إعداد اللعبة
        setupGame()

    func setupGame
        ? "إعداد اللعبة..."
        
        # إنشاء المشهد الرئيسي
        createMainScene()
        
        # إنشاء اللاعب
        createPlayer()
        
        # إنشاء الأعداء
        createEnemies()
        
        # إنشاء العناصر القابلة للجمع
        createCollectibles()
        
        # إعداد الإضاءة
        setupLighting()
        
        # إعداد الكاميرا
        setupCamera()
        
        # إعداد واجهة المستخدم
        setupUI()
        
        ? "تم إعداد اللعبة بنجاح"

    func createMainScene
        # إنشاء المشهد الرئيسي
        oMainScene = new Scene("MainScene")
        
        # إنشاء الأرضية
        oGround = new GameObject("Ground")
        oGround.setPosition([0, 0, 0])
        oGround.setScale([50, 1, 50])
        
        # إضافة مكون العرض للأرضية
        oGroundMesh = new MeshRenderer()
        oGroundMesh.createPlane(50, 50)
        oGroundMesh.setMaterial(createGroundMaterial())
        oGround.addComponent(oGroundMesh)
        
        # إضافة مكون الفيزياء للأرضية
        oGroundCollider = new BoxCollider()
        oGroundCollider.setStatic(true)
        oGround.addComponent(oGroundCollider)
        
        oMainScene.addGameObject(oGround)
        
        # تعيين المشهد الحالي
        oEngine.getSceneManager().setCurrentScene(oMainScene)

    func createPlayer
        # إنشاء كائن اللاعب
        oPlayer = new GameObject("Player")
        oPlayer.setPosition([0, 1, 0])
        
        # إضافة مكون العرض
        oPlayerMesh = new MeshRenderer()
        oPlayerMesh.createCube(1.0)
        oPlayerMesh.setMaterial(createPlayerMaterial())
        oPlayer.addComponent(oPlayerMesh)
        
        # إضافة مكون التحكم
        oPlayerController = new PlayerController()
        oPlayerController.setMovementSpeed(5.0)
        oPlayerController.setJumpHeight(2.0)
        oPlayer.addComponent(oPlayerController)
        
        # إضافة مكون الفيزياء
        oPlayerRigidBody = new RigidBody()
        oPlayerRigidBody.setMass(1.0)
        oPlayer.addComponent(oPlayerRigidBody)
        
        oPlayerCollider = new BoxCollider()
        oPlayer.addComponent(oPlayerCollider)
        
        # إضافة للمشهد
        oEngine.getSceneManager().getCurrentScene().addGameObject(oPlayer)

    func createEnemies
        # إنشاء الأعداء
        for i = 1 to 5 {
            oEnemy = createEnemy(i)
            add(aEnemies, oEnemy)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oEnemy)
        }

    func createEnemy nIndex
        # إنشاء عدو واحد
        oEnemy = new GameObject("Enemy_" + string(nIndex))
        
        # موقع عشوائي
        nX = (random(20) - 10)
        nZ = (random(20) - 10)
        oEnemy.setPosition([nX, 1, nZ])
        
        # مكون العرض
        oEnemyMesh = new MeshRenderer()
        oEnemyMesh.createCube(0.8)
        oEnemyMesh.setMaterial(createEnemyMaterial())
        oEnemy.addComponent(oEnemyMesh)
        
        # مكون الذكاء الاصطناعي
        oAIAgent = new AIAgent("guard")
        oAIAgent.setVisionRange(10.0)
        oAIAgent.setMovementSpeed(3.0)
        oEnemy.addComponent(oAIAgent)
        
        # مكون الفيزياء
        oEnemyCollider = new BoxCollider()
        oEnemy.addComponent(oEnemyCollider)
        
        return oEnemy

    func createCollectibles
        # إنشاء العناصر القابلة للجمع
        for i = 1 to 10 {
            oCollectible = createCollectible(i)
            add(aCollectibles, oCollectible)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oCollectible)
        }

    func createCollectible nIndex
        # إنشاء عنصر قابل للجمع
        oCollectible = new GameObject("Collectible_" + string(nIndex))
        
        # موقع عشوائي
        nX = (random(30) - 15)
        nZ = (random(30) - 15)
        oCollectible.setPosition([nX, 1.5, nZ])
        
        # مكون العرض
        oCollectibleMesh = new MeshRenderer()
        oCollectibleMesh.createSphere(0.3)
        oCollectibleMesh.setMaterial(createCollectibleMaterial())
        oCollectible.addComponent(oCollectibleMesh)
        
        # مكون الدوران
        oRotateComponent = new RotateComponent()
        oRotateComponent.setSpeed(90.0)
        oRotateComponent.setAxis([0, 1, 0])
        oCollectible.addComponent(oRotateComponent)
        
        # مكون الجسيمات
        oParticleEmitter = new ParticleEmitter("magic")
        oParticleEmitter.setParticlesPerSecond(20)
        oCollectible.addComponent(oParticleEmitter)
        
        # مكون التصادم
        oCollectibleCollider = new SphereCollider()
        oCollectibleCollider.setTrigger(true)
        oCollectible.addComponent(oCollectibleCollider)
        
        return oCollectible

    func setupLighting
        # إعداد الإضاءة
        oMainLight = new Light3D([0, 20, 0], WHITE, LIGHT_DIRECTIONAL)
        oMainLight.setIntensity(1.0)
        oMainLight.setDirection([0, -1, 0])
        
        oEngine.getSceneManager().getCurrentScene().addLight(oMainLight)
        
        # إضاءة إضافية
        oPointLight = new Light3D([10, 5, 10], Color(255, 200, 150, 255), LIGHT_POINT)
        oPointLight.setIntensity(0.8)
        oPointLight.setRange(15.0)
        
        oEngine.getSceneManager().getCurrentScene().addLight(oPointLight)

    func setupCamera
        # إعداد الكاميرا
        oCamera = new Camera([0, 15, 15], [0, 0, 0], [0, 1, 0])
        oCamera.setControlMode(CAMERA_CONTROL_THIRD_PERSON)
        oCamera.setMovementSpeed(8.0)
        
        # ربط الكاميرا باللاعب
        if oPlayer != null {
            oCameraFollow = new CameraFollowComponent()
            oCameraFollow.setTarget(oPlayer)
            oCameraFollow.setOffset([0, 10, 10])
            oCamera.addComponent(oCameraFollow)
        }
        
        oEngine.getSceneManager().getCurrentScene().addCamera(oCamera)
        oEngine.getSceneManager().getCurrentScene().setActiveCamera(oCamera)

    func setupUI
        # إعداد واجهة المستخدم
        ? "إعداد واجهة المستخدم..."

    func createGroundMaterial
        # إنشاء مادة الأرضية
        oMaterial = new Material("GroundMaterial")
        oMaterial.setAlbedoColor(Color(100, 150, 100, 255))
        oMaterial.setRoughness(0.8)
        oMaterial.setMetallic(0.0)
        return oMaterial

    func createPlayerMaterial
        # إنشاء مادة اللاعب
        oMaterial = new Material("PlayerMaterial")
        oMaterial.setAlbedoColor(Color(50, 150, 255, 255))
        oMaterial.setRoughness(0.3)
        oMaterial.setMetallic(0.1)
        return oMaterial

    func createEnemyMaterial
        # إنشاء مادة العدو
        oMaterial = new Material("EnemyMaterial")
        oMaterial.setAlbedoColor(Color(255, 50, 50, 255))
        oMaterial.setRoughness(0.4)
        oMaterial.setMetallic(0.0)
        return oMaterial

    func createCollectibleMaterial
        # إنشاء مادة العنصر القابل للجمع
        oMaterial = new Material("CollectibleMaterial")
        oMaterial.setAlbedoColor(Color(255, 255, 50, 255))
        oMaterial.setRoughness(0.1)
        oMaterial.setMetallic(0.8)
        oMaterial.setEmissiveStrength(0.3)
        oMaterial.setEmissiveColor(Color(255, 255, 100, 255))
        return oMaterial

    func start
        # بدء اللعبة
        ? "بدء اللعبة..."
        bGameRunning = true
        
        # تشغيل المحرك
        oEngine.start()

    func update nDeltaTime
        # تحديث منطق اللعبة
        if not bGameRunning {
            return
        }
        
        # فحص التصادمات
        checkCollisions()
        
        # تحديث النقاط
        updateScore()
        
        # فحص شروط الفوز/الخسارة
        checkGameConditions()

    func checkCollisions
        # فحص تصادمات اللاعب مع العناصر
        if oPlayer = null {
            return
        }
        
        aPlayerPos = oPlayer.getPosition()
        
        # فحص التصادم مع العناصر القابلة للجمع
        for i = len(aCollectibles) to 1 step -1 {
            oCollectible = aCollectibles[i]
            aCollectiblePos = oCollectible.getPosition()
            
            nDistance = calculateDistance(aPlayerPos, aCollectiblePos)
            if nDistance < 1.0 {
                # جمع العنصر
                collectItem(oCollectible, i)
            }
        }

    func collectItem oCollectible, nIndex
        # جمع عنصر
        nScore += 10
        del(aCollectibles, nIndex)
        
        # إزالة من المشهد
        oEngine.getSceneManager().getCurrentScene().removeGameObject(oCollectible)
        
        ? "تم جمع عنصر! النقاط: " + string(nScore)

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDy = aPos1[2] - aPos2[2]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

    func updateScore
        # تحديث النقاط والمستوى

    func checkGameConditions
        # فحص شروط الفوز/الخسارة
        if len(aCollectibles) = 0 {
            # فاز اللاعب
            winGame()
        }

    func winGame
        ? "تهانينا! لقد فزت!"
        ? "النقاط النهائية: " + string(nScore)
        bGameRunning = false

    func cleanup
        # تنظيف موارد اللعبة
        oEngine.cleanup()

    private
        oEngine
        oPlayer
        aEnemies
        aCollectibles
        nScore
        nLevel
        bGameRunning
}

# تشغيل اللعبة
oGame = new SimpleGame()
oGame.start()
