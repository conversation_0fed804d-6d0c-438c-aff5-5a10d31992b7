/*
مثال: عرض الفيزياء
الوصف: مثال يوضح إمكانيات محرك الفيزياء
المدخلات: تفاعل المستخدم
المخرجات: محاكاة فيزيائية واقعية
*/

load "../../game_engine.ring"

class PhysicsDemo {
    
    func init
        # إنشاء المحرك
        oEngine = new Engine(1280, 720, "عرض الفيزياء - Ring Game Engine")
        
        # متغيرات العرض
        aBoxes = []
        aSpheres = []
        oGround = null
        nSpawnTimer = 0.0
        nSpawnInterval = 2.0
        
        # إعداد العرض
        setupDemo()

    func setupDemo
        ? "إعداد عرض الفيزياء..."
        
        # إنشاء المشهد
        createScene()
        
        # إنشاء الأرضية
        createGround()
        
        # إنشاء الجدران
        createWalls()
        
        # إعداد الإضاءة
        setupLighting()
        
        # إعداد الكاميرا
        setupCamera()
        
        # إنشاء كائنات أولية
        createInitialObjects()
        
        ? "تم إعداد عرض الفيزياء"

    func createScene
        oMainScene = new Scene("PhysicsDemo")
        oEngine.getSceneManager().setCurrentScene(oMainScene)

    func createGround
        # إنشاء أرضية ثابتة
        oGround = new GameObject("Ground")
        oGround.setPosition([0, -1, 0])
        oGround.setScale([20, 1, 20])
        
        # مكون العرض
        oGroundMesh = new MeshRenderer()
        oGroundMesh.createCube(1.0)
        oGroundMesh.setMaterial(createGroundMaterial())
        oGround.addComponent(oGroundMesh)
        
        # مكون الفيزياء
        oGroundBody = new RigidBody()
        oGroundBody.setStatic(true)
        oGroundBody.setMass(0)
        oGround.addComponent(oGroundBody)
        
        oGroundCollider = new BoxCollider()
        oGroundCollider.setSize([20, 1, 20])
        oGround.addComponent(oGroundCollider)
        
        oEngine.getSceneManager().getCurrentScene().addGameObject(oGround)

    func createWalls
        # إنشاء جدران حول المنطقة
        aWallPositions = [
            [10, 2, 0],   # جدار شرقي
            [-10, 2, 0],  # جدار غربي
            [0, 2, 10],   # جدار شمالي
            [0, 2, -10]   # جدار جنوبي
        ]
        
        aWallScales = [
            [1, 4, 20],   # جدار شرقي
            [1, 4, 20],   # جدار غربي
            [20, 4, 1],   # جدار شمالي
            [20, 4, 1]    # جدار جنوبي
        ]
        
        for i = 1 to len(aWallPositions) {
            oWall = createWall(i, aWallPositions[i], aWallScales[i])
            oEngine.getSceneManager().getCurrentScene().addGameObject(oWall)
        }

    func createWall nIndex, aPos, aScale
        oWall = new GameObject("Wall_" + string(nIndex))
        oWall.setPosition(aPos)
        oWall.setScale(aScale)
        
        # مكون العرض
        oWallMesh = new MeshRenderer()
        oWallMesh.createCube(1.0)
        oWallMesh.setMaterial(createWallMaterial())
        oWall.addComponent(oWallMesh)
        
        # مكون الفيزياء
        oWallBody = new RigidBody()
        oWallBody.setStatic(true)
        oWall.addComponent(oWallBody)
        
        oWallCollider = new BoxCollider()
        oWallCollider.setSize(aScale)
        oWall.addComponent(oWallCollider)
        
        return oWall

    func setupLighting
        # إضاءة رئيسية
        oMainLight = new Light3D([0, 20, 0], WHITE, LIGHT_DIRECTIONAL)
        oMainLight.setIntensity(1.2)
        oMainLight.setDirection([0, -1, -0.3])
        oEngine.getSceneManager().getCurrentScene().addLight(oMainLight)
        
        # إضاءة نقطية ملونة
        oColorLight1 = new Light3D([5, 8, 5], Color(255, 100, 100, 255), LIGHT_POINT)
        oColorLight1.setIntensity(0.8)
        oColorLight1.setRange(12.0)
        oEngine.getSceneManager().getCurrentScene().addLight(oColorLight1)
        
        oColorLight2 = new Light3D([-5, 8, -5], Color(100, 100, 255, 255), LIGHT_POINT)
        oColorLight2.setIntensity(0.8)
        oColorLight2.setRange(12.0)
        oEngine.getSceneManager().getCurrentScene().addLight(oColorLight2)

    func setupCamera
        oCamera = new Camera([15, 10, 15], [0, 2, 0], [0, 1, 0])
        oCamera.setControlMode(CAMERA_CONTROL_ORBITAL)
        oCamera.setMovementSpeed(10.0)
        
        oEngine.getSceneManager().getCurrentScene().addCamera(oCamera)
        oEngine.getSceneManager().getCurrentScene().setActiveCamera(oCamera)

    func createInitialObjects
        # إنشاء كائنات أولية للعرض
        
        # إنشاء هرم من الصناديق
        createBoxPyramid([0, 1, 0], 4)
        
        # إنشاء كرات متدحرجة
        for i = 1 to 3 {
            createBouncingSphere([random(6) - 3, 8, random(6) - 3])
        }

    func createBoxPyramid aBasePos, nLevels
        # إنشاء هرم من الصناديق
        for nLevel = 0 to nLevels - 1 {
            nBoxesInLevel = nLevels - nLevel
            nStartX = -(nBoxesInLevel - 1) * 0.5
            
            for nBox = 0 to nBoxesInLevel - 1 {
                aPos = [
                    aBasePos[1] + nStartX + nBox,
                    aBasePos[2] + nLevel + 0.5,
                    aBasePos[3]
                ]
                
                oBox = createPhysicsBox(aPos)
                add(aBoxes, oBox)
                oEngine.getSceneManager().getCurrentScene().addGameObject(oBox)
            }
        }

    func createPhysicsBox aPos
        oBox = new GameObject("PhysicsBox_" + string(len(aBoxes) + 1))
        oBox.setPosition(aPos)
        
        # مكون العرض
        oBoxMesh = new MeshRenderer()
        oBoxMesh.createCube(1.0)
        oBoxMesh.setMaterial(createBoxMaterial())
        oBox.addComponent(oBoxMesh)
        
        # مكون الفيزياء
        oBoxBody = new RigidBody()
        oBoxBody.setMass(1.0)
        oBoxBody.setRestitution(0.3)  # ارتداد
        oBoxBody.setFriction(0.7)     # احتكاك
        oBox.addComponent(oBoxBody)
        
        oBoxCollider = new BoxCollider()
        oBoxCollider.setSize([1, 1, 1])
        oBox.addComponent(oBoxCollider)
        
        return oBox

    func createBouncingSphere aPos
        oSphere = new GameObject("BouncingSphere_" + string(len(aSpheres) + 1))
        oSphere.setPosition(aPos)
        
        # مكون العرض
        oSphereMesh = new MeshRenderer()
        oSphereMesh.createSphere(0.5)
        oSphereMesh.setMaterial(createSphereMaterial())
        oSphere.addComponent(oSphereMesh)
        
        # مكون الفيزياء
        oSphereBody = new RigidBody()
        oSphereBody.setMass(0.5)
        oSphereBody.setRestitution(0.8)  # ارتداد عالي
        oSphereBody.setFriction(0.3)     # احتكاك قليل
        
        # إضافة سرعة أولية عشوائية
        aInitialVelocity = [
            (random(4) - 2),
            random(3) + 2,
            (random(4) - 2)
        ]
        oSphereBody.setVelocity(aInitialVelocity)
        
        oSphere.addComponent(oSphereBody)
        
        oSphereCollider = new SphereCollider()
        oSphereCollider.setRadius(0.5)
        oSphere.addComponent(oSphereCollider)
        
        add(aSpheres, oSphere)
        oEngine.getSceneManager().getCurrentScene().addGameObject(oSphere)

    func update nDeltaTime
        # تحديث العرض
        nSpawnTimer += nDeltaTime
        
        # إنشاء كائنات جديدة بشكل دوري
        if nSpawnTimer >= nSpawnInterval {
            spawnRandomObject()
            nSpawnTimer = 0.0
        }
        
        # معالجة المدخلات
        handleInput()
        
        # تنظيف الكائنات التي سقطت
        cleanupFallenObjects()

    func spawnRandomObject
        # إنشاء كائن عشوائي
        nType = random(2)
        aSpawnPos = [random(8) - 4, 10, random(8) - 4]
        
        if nType = 1 {
            # إنشاء صندوق
            oBox = createPhysicsBox(aSpawnPos)
            add(aBoxes, oBox)
            oEngine.getSceneManager().getCurrentScene().addGameObject(oBox)
        else
            # إنشاء كرة
            createBouncingSphere(aSpawnPos)
        }

    func handleInput
        # معالجة مدخلات المستخدم
        if IsKeyPressed(KEY_SPACE) {
            # إنشاء انفجار
            createExplosion([0, 5, 0], 10.0)
        }
        
        if IsKeyPressed(KEY_R) {
            # إعادة تعيين العرض
            resetDemo()
        }
        
        if IsMouseButtonPressed(MOUSE_LEFT_BUTTON) {
            # إنشاء كائن في موقع النقر
            spawnObjectAtMouse()
        }

    func createExplosion aCenter, nForce
        # إنشاء انفجار يؤثر على الكائنات المجاورة
        ? "انفجار في الموقع: " + string(aCenter[1]) + ", " + string(aCenter[2]) + ", " + string(aCenter[3])
        
        # تطبيق قوة على جميع الكائنات القريبة
        aAllObjects = aBoxes + aSpheres
        
        for oObject in aAllObjects {
            aObjectPos = oObject.getPosition()
            nDistance = calculateDistance(aCenter, aObjectPos)
            
            if nDistance < 8.0 and nDistance > 0.1 {
                # حساب اتجاه القوة
                aDirection = [
                    aObjectPos[1] - aCenter[1],
                    aObjectPos[2] - aCenter[2],
                    aObjectPos[3] - aCenter[3]
                ]
                
                # تطبيع الاتجاه
                nLength = sqrt(aDirection[1]^2 + aDirection[2]^2 + aDirection[3]^2)
                if nLength > 0 {
                    aDirection[1] /= nLength
                    aDirection[2] /= nLength
                    aDirection[3] /= nLength
                }
                
                # حساب قوة الانفجار (تقل مع المسافة)
                nExplosionForce = nForce / (nDistance + 1)
                
                # تطبيق القوة
                oRigidBody = oObject.getComponent("RigidBody")
                if oRigidBody != null {
                    aForce = [
                        aDirection[1] * nExplosionForce,
                        aDirection[2] * nExplosionForce,
                        aDirection[3] * nExplosionForce
                    ]
                    oRigidBody.addForce(aForce)
                }
            }
        }

    func spawnObjectAtMouse
        # إنشاء كائن في موقع النقر
        aMousePos = GetMousePosition()
        oRay = GetMouseRay(aMousePos, oEngine.getRenderer().getCurrentCamera().getRayLibCamera())
        
        # حساب نقطة الإنشاء (على ارتفاع ثابت)
        aSpawnPos = [oRay.position.x, 8, oRay.position.z]
        
        # إنشاء كرة في الموقع
        createBouncingSphere(aSpawnPos)

    func cleanupFallenObjects
        # تنظيف الكائنات التي سقطت تحت مستوى معين
        nMinY = -10
        
        # تنظيف الصناديق
        for i = len(aBoxes) to 1 step -1 {
            oBox = aBoxes[i]
            aPos = oBox.getPosition()
            if aPos[2] < nMinY {
                oEngine.getSceneManager().getCurrentScene().removeGameObject(oBox)
                del(aBoxes, i)
            }
        }
        
        # تنظيف الكرات
        for i = len(aSpheres) to 1 step -1 {
            oSphere = aSpheres[i]
            aPos = oSphere.getPosition()
            if aPos[2] < nMinY {
                oEngine.getSceneManager().getCurrentScene().removeGameObject(oSphere)
                del(aSpheres, i)
            }
        }

    func resetDemo
        # إعادة تعيين العرض
        ? "إعادة تعيين العرض..."
        
        # إزالة جميع الكائنات
        for oBox in aBoxes {
            oEngine.getSceneManager().getCurrentScene().removeGameObject(oBox)
        }
        for oSphere in aSpheres {
            oEngine.getSceneManager().getCurrentScene().removeGameObject(oSphere)
        }
        
        aBoxes = []
        aSpheres = []
        
        # إعادة إنشاء الكائنات الأولية
        createInitialObjects()

    func calculateDistance aPos1, aPos2
        nDx = aPos1[1] - aPos2[1]
        nDy = aPos1[2] - aPos2[2]
        nDz = aPos1[3] - aPos2[3]
        return sqrt(nDx*nDx + nDy*nDy + nDz*nDz)

    func createGroundMaterial
        oMaterial = new Material("GroundMaterial")
        oMaterial.setAlbedoColor(Color(80, 120, 80, 255))
        oMaterial.setRoughness(0.9)
        oMaterial.setMetallic(0.0)
        return oMaterial

    func createWallMaterial
        oMaterial = new Material("WallMaterial")
        oMaterial.setAlbedoColor(Color(120, 120, 120, 255))
        oMaterial.setRoughness(0.7)
        oMaterial.setMetallic(0.1)
        return oMaterial

    func createBoxMaterial
        oMaterial = new Material("BoxMaterial")
        oMaterial.setAlbedoColor(Color(200, 150, 100, 255))
        oMaterial.setRoughness(0.5)
        oMaterial.setMetallic(0.0)
        return oMaterial

    func createSphereMaterial
        oMaterial = new Material("SphereMaterial")
        oMaterial.setAlbedoColor(Color(100, 150, 200, 255))
        oMaterial.setRoughness(0.2)
        oMaterial.setMetallic(0.8)
        return oMaterial

    func start
        ? "بدء عرض الفيزياء..."
        ? "التحكم:"
        ? "- مسطرة المسافة: انفجار"
        ? "- R: إعادة تعيين"
        ? "- النقر بالفأرة: إنشاء كرة"
        ? "- الفأرة الوسطى: تحريك الكاميرا"
        
        oEngine.start()

    private
        oEngine
        aBoxes
        aSpheres
        oGround
        nSpawnTimer
        nSpawnInterval
}

# تشغيل العرض
oDemo = new PhysicsDemo()
oDemo.start()
