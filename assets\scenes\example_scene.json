{"scene": {"name": "Example Scene", "id": "example_scene_001", "version": "1.0", "description": "م<PERSON>هد مثال يوضح إمكانيات المحرك"}, "environment": {"ambientColor": [0.2, 0.2, 0.2, 1.0], "skyboxColor": [0.5, 0.8, 1.0, 1.0], "fog": {"enabled": false, "density": 0.01, "color": [0.7, 0.7, 0.7, 1.0]}}, "cameras": [{"name": "MainCamera", "type": "perspective", "position": [0, 10, 10], "target": [0, 0, 0], "up": [0, 1, 0], "fov": 45, "nearPlane": 0.1, "farPlane": 1000.0, "active": true}], "lights": [{"name": "DirectionalLight", "type": "directional", "position": [0, 10, 0], "direction": [0, -1, 0], "color": [1, 1, 1, 1], "intensity": 1.0, "castShadows": true, "enabled": true}, {"name": "PointLight1", "type": "point", "position": [5, 3, 5], "color": [1, 0.8, 0.6, 1], "intensity": 0.8, "range": 10.0, "attenuation": 1.0, "enabled": true}], "gameObjects": [{"name": "Ground", "id": "ground_001", "position": [0, 0, 0], "rotation": [0, 0, 0], "scale": [20, 1, 20], "active": true, "visible": true, "components": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesh": "plane", "material": "ground_material"}, {"type": "Collider", "shape": "box", "static": true}]}, {"name": "Player", "id": "player_001", "position": [0, 1, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "active": true, "visible": true, "components": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesh": "cube", "material": "player_material"}, {"type": "Collider", "shape": "box", "static": false}, {"type": "PlayerController", "speed": 5.0, "jumpHeight": 2.0}]}, {"name": "Enemy", "id": "enemy_001", "position": [10, 1, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "active": true, "visible": true, "components": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesh": "cube", "material": "enemy_material"}, {"type": "Collider", "shape": "box", "static": false}, {"type": "AIAgent", "agentType": "guard", "visionRange": 15.0, "hearingRange": 20.0}]}, {"name": "Collectible", "id": "collectible_001", "position": [5, 1.5, 5], "rotation": [0, 0, 0], "scale": [0.5, 0.5, 0.5], "active": true, "visible": true, "components": [{"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mesh": "sphere", "material": "collectible_material"}, {"type": "Collider", "shape": "sphere", "static": false, "trigger": true}, {"type": "RotateComponent", "speed": 90.0, "axis": [0, 1, 0]}, {"type": "ParticleEmitter", "emitterType": "magic", "particlesPerSecond": 50}]}], "materials": [{"name": "ground_material", "albedoColor": [0.5, 0.5, 0.5, 1], "roughness": 0.8, "metallic": 0.0, "textures": {"albedo": "ground_diffuse.png", "normal": "ground_normal.png"}}, {"name": "player_material", "albedoColor": [0.2, 0.6, 1.0, 1], "roughness": 0.3, "metallic": 0.1, "emissiveStrength": 0.0}, {"name": "enemy_material", "albedoColor": [1.0, 0.2, 0.2, 1], "roughness": 0.4, "metallic": 0.0, "emissiveStrength": 0.1}, {"name": "collectible_material", "albedoColor": [1.0, 1.0, 0.2, 1], "roughness": 0.1, "metallic": 0.8, "emissiveStrength": 0.3, "emissiveColor": [1.0, 1.0, 0.5, 1]}], "particleEmitters": [{"name": "MagicEffect", "type": "magic", "position": [5, 1.5, 5], "particlesPerSecond": 50, "lifetime": 2.0, "startColor": [0.8, 0.2, 1.0, 1], "endColor": [0.4, 0.8, 1.0, 0], "startSize": 0.3, "endSize": 0.1, "enabled": true}], "audio": [{"name": "BackgroundMusic", "file": "background_music.mp3", "type": "music", "volume": 0.6, "loop": true, "autoPlay": true}, {"name": "CollectSound", "file": "collect.wav", "type": "sound", "volume": 0.8, "loop": false, "trigger": "onCollect"}], "scripts": [{"name": "GameLogic", "file": "game_logic.ring", "autoLoad": true}, {"name": "PlayerController", "file": "player_controller.ring", "autoLoad": true}]}