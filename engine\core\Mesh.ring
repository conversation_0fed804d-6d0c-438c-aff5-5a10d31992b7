/*
الكلاس: Mesh
الوصف: إدارة الشبكات ثلاثية الأبعاد والهندسة
المدخلات: بيانات الرؤوس والمثلثات
المخرجات: شبكة ثلاثية الأبعاد قابلة للرسم
*/

load "raylib.ring"

class Mesh {
    
    func init cMeshName = "DefaultMesh"
        cName = cMeshName
        cID = generateMeshID()
        
        # بيانات الشبكة
        aVertices = []      # مواقع الرؤوس
        aNormals = []       # اتجاهات الرؤوس
        aTexCoords = []     # إحداثيات القوام
        aColors = []        # ألوان الرؤوس
        aIndices = []       # فهارس المثلثات
        aTangents = []      # المماسات للإضاءة المتقدمة
        
        # إحصائيات الشبكة
        nVertexCount = 0
        nTriangleCount = 0
        nIndexCount = 0
        
        # حدود الشبكة (Bounding Box)
        aBoundingMin = [0, 0, 0]
        aBoundingMax = [0, 0, 0]
        aBoundingCenter = [0, 0, 0]
        nBoundingRadius = 0
        
        # خصائص الشبكة
        bHasNormals = false
        bHasTexCoords = false
        bHasColors = false
        bHasTangents = false
        bIsStatic = true
        bIsVisible = true
        
        # مادة الشبكة
        oMaterial = null
        
        # معرف RayLib للشبكة
        oRayLibMesh = null

    func generateMeshID
        return "MESH_" + string(clock()) + "_" + string(random(9999))

    func loadFromFile cFilePath
        # تحميل الشبكة من ملف
        try {
            oModel = LoadModel(cFilePath)
            if IsModelReady(oModel) {
                # استخراج بيانات الشبكة من النموذج
                extractMeshData(oModel)
                calculateBounds()
                return true
            }
        catch
            ? "خطأ في تحميل الشبكة: " + cFilePath
        }
        return false

    func extractMeshData oModel
        # استخراج بيانات الشبكة من نموذج RayLib
        # ملاحظة: هذا يتطلب وصول مباشر لبيانات النموذج
        oRayLibMesh = oModel.meshes[0]  # أخذ أول شبكة
        
        # نسخ بيانات الرؤوس
        nVertexCount = oRayLibMesh.vertexCount
        nTriangleCount = oRayLibMesh.triangleCount
        
        # تحديث الإحصائيات
        updateStatistics()

    func createCube nSize = 1.0
        # إنشاء مكعب
        clearMeshData()
        
        nHalf = nSize / 2.0
        
        # رؤوس المكعب
        aVertices = [
            # الوجه الأمامي
            [-nHalf, -nHalf,  nHalf],  # 0
            [ nHalf, -nHalf,  nHalf],  # 1
            [ nHalf,  nHalf,  nHalf],  # 2
            [-nHalf,  nHalf,  nHalf],  # 3
            # الوجه الخلفي
            [-nHalf, -nHalf, -nHalf],  # 4
            [ nHalf, -nHalf, -nHalf],  # 5
            [ nHalf,  nHalf, -nHalf],  # 6
            [-nHalf,  nHalf, -nHalf]   # 7
        ]
        
        # فهارس المثلثات
        aIndices = [
            # الوجه الأمامي
            0, 1, 2,  2, 3, 0,
            # الوجه الخلفي
            4, 6, 5,  6, 4, 7,
            # الوجه الأيسر
            4, 0, 3,  3, 7, 4,
            # الوجه الأيمن
            1, 5, 6,  6, 2, 1,
            # الوجه العلوي
            3, 2, 6,  6, 7, 3,
            # الوجه السفلي
            4, 5, 1,  1, 0, 4
        ]
        
        generateNormals()
        generateTexCoords()
        calculateBounds()
        updateStatistics()

    func createSphere nRadius = 1.0, nSegments = 16
        # إنشاء كرة
        clearMeshData()
        
        nRings = nSegments / 2
        
        # إنشاء رؤوس الكرة
        for nRing = 0 to nRings {
            nPhi = 3.14159 * nRing / nRings
            for nSegment = 0 to nSegments {
                nTheta = 2.0 * 3.14159 * nSegment / nSegments
                
                nX = nRadius * sin(nPhi) * cos(nTheta)
                nY = nRadius * cos(nPhi)
                nZ = nRadius * sin(nPhi) * sin(nTheta)
                
                add(aVertices, [nX, nY, nZ])
                
                # إحداثيات القوام
                nU = nSegment / nSegments
                nV = nRing / nRings
                add(aTexCoords, [nU, nV])
                
                # الاتجاهات (نفس موقع الرأس للكرة)
                add(aNormals, [nX/nRadius, nY/nRadius, nZ/nRadius])
            }
        }
        
        # إنشاء فهارس المثلثات
        for nRing = 0 to nRings-1 {
            for nSegment = 0 to nSegments-1 {
                nCurrent = nRing * (nSegments + 1) + nSegment
                nNext = nCurrent + nSegments + 1
                
                # مثلث أول
                add(aIndices, nCurrent)
                add(aIndices, nNext)
                add(aIndices, nCurrent + 1)
                
                # مثلث ثاني
                add(aIndices, nCurrent + 1)
                add(aIndices, nNext)
                add(aIndices, nNext + 1)
            }
        }
        
        bHasNormals = true
        bHasTexCoords = true
        calculateBounds()
        updateStatistics()

    func createPlane nWidth = 1.0, nHeight = 1.0
        # إنشاء مستوى
        clearMeshData()
        
        nHalfW = nWidth / 2.0
        nHalfH = nHeight / 2.0
        
        # رؤوس المستوى
        aVertices = [
            [-nHalfW, 0, -nHalfH],  # 0
            [ nHalfW, 0, -nHalfH],  # 1
            [ nHalfW, 0,  nHalfH],  # 2
            [-nHalfW, 0,  nHalfH]   # 3
        ]
        
        # اتجاهات المستوى (كلها لأعلى)
        aNormals = [
            [0, 1, 0],
            [0, 1, 0],
            [0, 1, 0],
            [0, 1, 0]
        ]
        
        # إحداثيات القوام
        aTexCoords = [
            [0, 0],
            [1, 0],
            [1, 1],
            [0, 1]
        ]
        
        # فهارس المثلثات
        aIndices = [0, 1, 2,  2, 3, 0]
        
        bHasNormals = true
        bHasTexCoords = true
        calculateBounds()
        updateStatistics()

    func clearMeshData
        aVertices = []
        aNormals = []
        aTexCoords = []
        aColors = []
        aIndices = []
        aTangents = []
        
        nVertexCount = 0
        nTriangleCount = 0
        nIndexCount = 0
        
        bHasNormals = false
        bHasTexCoords = false
        bHasColors = false
        bHasTangents = false

    func generateNormals
        # توليد الاتجاهات تلقائياً
        aNormals = []
        
        # تهيئة الاتجاهات بالصفر
        for i = 1 to len(aVertices) {
            add(aNormals, [0, 0, 0])
        }
        
        # حساب اتجاهات المثلثات
        for i = 1 to len(aIndices) step 3 {
            nIdx1 = aIndices[i] + 1
            nIdx2 = aIndices[i+1] + 1
            nIdx3 = aIndices[i+2] + 1
            
            if nIdx1 <= len(aVertices) and nIdx2 <= len(aVertices) and nIdx3 <= len(aVertices) {
                aV1 = aVertices[nIdx1]
                aV2 = aVertices[nIdx2]
                aV3 = aVertices[nIdx3]
                
                # حساب الاتجاه العمودي للمثلث
                aNormal = calculateTriangleNormal(aV1, aV2, aV3)
                
                # إضافة الاتجاه لكل رأس
                aNormals[nIdx1][1] += aNormal[1]
                aNormals[nIdx1][2] += aNormal[2]
                aNormals[nIdx1][3] += aNormal[3]
                
                aNormals[nIdx2][1] += aNormal[1]
                aNormals[nIdx2][2] += aNormal[2]
                aNormals[nIdx2][3] += aNormal[3]
                
                aNormals[nIdx3][1] += aNormal[1]
                aNormals[nIdx3][2] += aNormal[2]
                aNormals[nIdx3][3] += aNormal[3]
            }
        }
        
        # تطبيع الاتجاهات
        for i = 1 to len(aNormals) {
            aNormals[i] = normalizeVector(aNormals[i])
        }
        
        bHasNormals = true

    func calculateTriangleNormal aV1, aV2, aV3
        # حساب الاتجاه العمودي للمثلث
        aEdge1 = [aV2[1] - aV1[1], aV2[2] - aV1[2], aV2[3] - aV1[3]]
        aEdge2 = [aV3[1] - aV1[1], aV3[2] - aV1[2], aV3[3] - aV1[3]]
        
        # الضرب الاتجاهي
        aNormal = [
            aEdge1[2] * aEdge2[3] - aEdge1[3] * aEdge2[2],
            aEdge1[3] * aEdge2[1] - aEdge1[1] * aEdge2[3],
            aEdge1[1] * aEdge2[2] - aEdge1[2] * aEdge2[1]
        ]
        
        return normalizeVector(aNormal)

    func normalizeVector aVec
        nLength = sqrt(aVec[1]*aVec[1] + aVec[2]*aVec[2] + aVec[3]*aVec[3])
        if nLength > 0 {
            return [aVec[1]/nLength, aVec[2]/nLength, aVec[3]/nLength]
        }
        return [0, 0, 0]

    func generateTexCoords
        # توليد إحداثيات قوام بسيطة
        aTexCoords = []
        for i = 1 to len(aVertices) {
            # إحداثيات بسيطة بناءً على الموقع
            aVertex = aVertices[i]
            nU = (aVertex[1] + 1.0) / 2.0
            nV = (aVertex[3] + 1.0) / 2.0
            add(aTexCoords, [nU, nV])
        }
        bHasTexCoords = true

    func calculateBounds
        # حساب حدود الشبكة
        if len(aVertices) = 0 {
            return
        }
        
        aBoundingMin = [aVertices[1][1], aVertices[1][2], aVertices[1][3]]
        aBoundingMax = [aVertices[1][1], aVertices[1][2], aVertices[1][3]]
        
        for aVertex in aVertices {
            # الحد الأدنى
            if aVertex[1] < aBoundingMin[1] { aBoundingMin[1] = aVertex[1] }
            if aVertex[2] < aBoundingMin[2] { aBoundingMin[2] = aVertex[2] }
            if aVertex[3] < aBoundingMin[3] { aBoundingMin[3] = aVertex[3] }
            
            # الحد الأقصى
            if aVertex[1] > aBoundingMax[1] { aBoundingMax[1] = aVertex[1] }
            if aVertex[2] > aBoundingMax[2] { aBoundingMax[2] = aVertex[2] }
            if aVertex[3] > aBoundingMax[3] { aBoundingMax[3] = aVertex[3] }
        }
        
        # حساب المركز
        aBoundingCenter = [
            (aBoundingMin[1] + aBoundingMax[1]) / 2.0,
            (aBoundingMin[2] + aBoundingMax[2]) / 2.0,
            (aBoundingMin[3] + aBoundingMax[3]) / 2.0
        ]
        
        # حساب نصف القطر
        nBoundingRadius = 0
        for aVertex in aVertices {
            nDistance = sqrt(
                (aVertex[1] - aBoundingCenter[1]) * (aVertex[1] - aBoundingCenter[1]) +
                (aVertex[2] - aBoundingCenter[2]) * (aVertex[2] - aBoundingCenter[2]) +
                (aVertex[3] - aBoundingCenter[3]) * (aVertex[3] - aBoundingCenter[3])
            )
            if nDistance > nBoundingRadius {
                nBoundingRadius = nDistance
            }
        }

    func updateStatistics
        nVertexCount = len(aVertices)
        nIndexCount = len(aIndices)
        nTriangleCount = nIndexCount / 3

    func setMaterial oMat
        oMaterial = oMat

    func getMaterial
        return oMaterial

    func draw mTransform
        # رسم الشبكة
        if not bIsVisible or len(aVertices) = 0 {
            return
        }
        
        # تطبيق المادة إذا كانت موجودة
        if oMaterial != null {
            oMaterial.apply()
        }
        
        # رسم الشبكة باستخدام RayLib
        # ملاحظة: هذا يتطلب تحويل البيانات لتنسيق RayLib
        if oRayLibMesh != null {
            DrawMesh(oRayLibMesh, oMaterial, mTransform)
        }

    func getName
        return cName

    func setName cNewName
        cName = cNewName

    func getID
        return cID

    func getVertexCount
        return nVertexCount

    func getTriangleCount
        return nTriangleCount

    func getBoundingMin
        return aBoundingMin

    func getBoundingMax
        return aBoundingMax

    func getBoundingCenter
        return aBoundingCenter

    func getBoundingRadius
        return nBoundingRadius

    func isVisible
        return bIsVisible

    func setVisible bState
        bIsVisible = bState

    private
        cName
        cID
        aVertices
        aNormals
        aTexCoords
        aColors
        aIndices
        aTangents
        nVertexCount
        nTriangleCount
        nIndexCount
        aBoundingMin
        aBoundingMax
        aBoundingCenter
        nBoundingRadius
        bHasNormals
        bHasTexCoords
        bHasColors
        bHasTangents
        bIsStatic
        bIsVisible
        oMaterial
        oRayLibMesh
}
