/*
الكلاس: SceneManager
الوصف: إدارة المشاهد والتنقل بينها
المدخلات: مشاهد اللعبة
المخرجات: إدارة متقدمة للمشاهد
*/

load "Scene.ring"
load "SceneGraph.ring"

class SceneManager {

    func init
        aScenes = []
        aSceneMap = []  # خريطة سريعة للبحث بالاسم
        oCurrentScene = null
        oNextScene = null
        bTransitioning = false
        nTransitionTime = 0.0
        nTransitionDuration = 1.0

    func addScene oScene
        if oScene != null and find(aScenes, oScene) = 0 {
            add(aScenes, oScene)
            aSceneMap[oScene.getName()] = oScene

            # إذا لم يكن هناك مشهد حالي، اجعل هذا هو المشهد الحالي
            if oCurrentScene = null {
                setCurrentScene(oScene)
            }
            return true
        }
        return false

    func removeScene oScene
        if oScene != null {
            nIndex = find(aScenes, oScene)
            if nIndex > 0 {
                # تنظيف المشهد
                oScene.cleanup()

                # إزالة من القوائم
                del(aScenes, nIndex)
                del(aSceneMap, oScene.getName())

                # إذا كان هذا هو المشهد الحالي، قم بتغييره
                if oCurrentScene = oScene {
                    if len(aScenes) > 0 {
                        setCurrentScene(aScenes[1])
                    else
                        oCurrentScene = null
                    }
                }
                return true
            }
        }
        return false

    func loadScene cScenePath, cSceneName = ""
        if cSceneName = "" {
            # استخراج اسم المشهد من المسار
            aPathParts = split(cScenePath, "/")
            cFileName = aPathParts[len(aPathParts)]
            aNameParts = split(cFileName, ".")
            cSceneName = aNameParts[1]
        }

        oNewScene = new Scene(cSceneName)
        if oNewScene.load(cScenePath) {
            addScene(oNewScene)
            return oNewScene
        }
        return null

    func setCurrentScene oScene
        if oScene != null and find(aScenes, oScene) > 0 {
            if oCurrentScene != null {
                oCurrentScene.onExit()
            }
            oCurrentScene = oScene
            oCurrentScene.onEnter()
            return true
        }
        return false

    func setCurrentSceneByName cSceneName
        oScene = getScene(cSceneName)
        if oScene != null {
            return setCurrentScene(oScene)
        }
        return false

    func transitionToScene oScene, nDuration = 1.0
        if oScene != null and find(aScenes, oScene) > 0 {
            oNextScene = oScene
            nTransitionDuration = nDuration
            nTransitionTime = 0.0
            bTransitioning = true
            return true
        }
        return false

    func transitionToSceneByName cSceneName, nDuration = 1.0
        oScene = getScene(cSceneName)
        if oScene != null {
            return transitionToScene(oScene, nDuration)
        }
        return false

    func update nDeltaTime
        # تحديث الانتقال إذا كان نشطاً
        if bTransitioning {
            updateTransition(nDeltaTime)
        }

        # تحديث المشهد الحالي
        if oCurrentScene != null {
            oCurrentScene.update(nDeltaTime)
        }

    func updateTransition nDeltaTime
        nTransitionTime += nDeltaTime

        if nTransitionTime >= nTransitionDuration {
            # انتهاء الانتقال
            setCurrentScene(oNextScene)
            oNextScene = null
            bTransitioning = false
            nTransitionTime = 0.0
        }

    func render
        if oCurrentScene != null {
            oCurrentScene.render()

            # رسم تأثير الانتقال إذا كان نشطاً
            if bTransitioning {
                renderTransition()
            }
        }

    func renderTransition
        # رسم تأثير انتقال بسيط (تلاشي)
        nAlpha = nTransitionTime / nTransitionDuration
        if nAlpha > 0.5 {
            nAlpha = 1.0 - nAlpha
        }
        nAlpha *= 2.0  # جعل التأثير أكثر وضوحاً

        # رسم مستطيل شفاف
        DrawRectangle(0, 0, GetScreenWidth(), GetScreenHeight(),
                     Fade(BLACK, nAlpha))

    func getCurrentScene
        return oCurrentScene

    func getScene cSceneName
        return aSceneMap[cSceneName]

    func getScenes
        return aScenes

    func getSceneCount
        return len(aScenes)

    func addGameObject oObject, cParentID = ""
        if oCurrentScene != null {
            return oCurrentScene.addGameObject(oObject)
        }
        return false

    func removeGameObject oObject
        if oCurrentScene != null {
            return oCurrentScene.removeGameObject(oObject)
        }
        return false

    func findGameObject cName
        if oCurrentScene != null {
            return oCurrentScene.findGameObject(cName)
        }
        return null

    func findGameObjectByID cID
        if oCurrentScene != null {
            return oCurrentScene.findGameObjectByID(cID)
        }
        return null

    func saveCurrentScene cFilePath = ""
        if oCurrentScene != null {
            return oCurrentScene.save(cFilePath)
        }
        return false

    func isTransitioning
        return bTransitioning

    func getTransitionProgress
        if bTransitioning {
            return nTransitionTime / nTransitionDuration
        }
        return 0.0

    func cleanup
        # تنظيف جميع المشاهد
        for oScene in aScenes {
            oScene.cleanup()
        }
        aScenes = []
        aSceneMap = []
        oCurrentScene = null
        oNextScene = null
        bTransitioning = false

    private
        aScenes
        aSceneMap
        oCurrentScene
        oNextScene
        bTransitioning
        nTransitionTime
        nTransitionDuration
}
