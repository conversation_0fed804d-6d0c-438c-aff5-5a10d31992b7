/*
الكلاس: BehaviorTree
الوصف: شجرة سلوك للذكاء الاصطناعي مع عقد مختلفة
المدخلات: عقد السلوك والشروط
المخرجات: سلوك ذكي ومرن
*/

# حالات العقد
NODE_SUCCESS = 0
NODE_FAILURE = 1
NODE_RUNNING = 2

class BehaviorTree {
    
    func init cTreeName = "BehaviorTree"
        cName = cTreeName
        cID = generateTreeID()
        oRootNode = null
        aNodes = []
        bActive = true
        bDebugMode = false
        
        # إحصائيات الشجرة
        nExecutionCount = 0
        nSuccessCount = 0
        nFailureCount = 0
        nLastExecutionTime = 0

    func generateTreeID
        return "BT_" + string(clock()) + "_" + string(random(99999))

    # إنشاء العقد
    func createSequenceNode cName = "Sequence"
        oNode = new SequenceNode(cName)
        add(aNodes, oNode)
        return oNode

    func createSelectorNode cName = "Selector"
        oNode = new SelectorNode(cName)
        add(aNodes, oNode)
        return oNode

    func createParallelNode cName = "Parallel"
        oNode = new ParallelNode(cName)
        add(aNodes, oNode)
        return oNode

    func createDecoratorNode cName = "Decorator", cType = "inverter"
        oNode = new DecoratorNode(cName, cType)
        add(aNodes, oNode)
        return oNode

    func createActionNode cName, fpAction
        oNode = new ActionNode(cName, fpAction)
        add(aNodes, oNode)
        return oNode

    func createConditionNode cName, fpCondition
        oNode = new ConditionNode(cName, fpCondition)
        add(aNodes, oNode)
        return oNode

    func setRootNode oNode
        oRootNode = oNode

    func getRootNode
        return oRootNode

    func update nDeltaTime
        if not bActive or oRootNode = null {
            return NODE_FAILURE
        }
        
        nStartTime = GetTime()
        nResult = oRootNode.execute(nDeltaTime)
        nLastExecutionTime = GetTime() - nStartTime
        
        # تحديث الإحصائيات
        nExecutionCount++
        if nResult = NODE_SUCCESS {
            nSuccessCount++
        elseif nResult = NODE_FAILURE
            nFailureCount++
        }
        
        if bDebugMode {
            ? "BehaviorTree [" + cName + "] Result: " + getResultString(nResult)
        }
        
        return nResult

    func getResultString nResult
        switch nResult
        on NODE_SUCCESS return "SUCCESS"
        on NODE_FAILURE return "FAILURE"
        on NODE_RUNNING return "RUNNING"
        other return "UNKNOWN"
        off

    func setActive bState
        bActive = bState

    func isActive
        return bActive

    func setDebugMode bState
        bDebugMode = bState

    func getStatistics
        return [
            :name = cName,
            :id = cID,
            :executionCount = nExecutionCount,
            :successCount = nSuccessCount,
            :failureCount = nFailureCount,
            :successRate = getSuccessRate(),
            :lastExecutionTime = nLastExecutionTime
        ]

    func getSuccessRate
        if nExecutionCount = 0 {
            return 0.0
        }
        return nSuccessCount / nExecutionCount

    func reset
        # إعادة تعيين الشجرة
        if oRootNode != null {
            oRootNode.reset()
        }

    func cleanup
        # تنظيف الشجرة
        for oNode in aNodes {
            oNode.cleanup()
        }
        aNodes = []
        oRootNode = null

    func getName
        return cName

    func getID
        return cID

    private
        cName
        cID
        oRootNode
        aNodes
        bActive
        bDebugMode
        nExecutionCount
        nSuccessCount
        nFailureCount
        nLastExecutionTime
}

# كلاس العقدة الأساسية
class BehaviorNode {
    
    func init cNodeName = "Node"
        cName = cNodeName
        cID = generateNodeID()
        oParent = null
        aChildren = []
        nLastResult = NODE_FAILURE
        bIsRunning = false

    func generateNodeID
        return "NODE_" + string(clock()) + "_" + string(random(99999))

    func execute nDeltaTime
        # يجب تنفيذها في الكلاسات المشتقة
        return NODE_FAILURE

    func addChild oChild
        if oChild != null {
            add(aChildren, oChild)
            oChild.setParent(this)
        }

    func removeChild oChild
        nIndex = find(aChildren, oChild)
        if nIndex > 0 {
            del(aChildren, nIndex)
            oChild.setParent(null)
        }

    func setParent oParentNode
        oParent = oParentNode

    func getParent
        return oParent

    func getChildren
        return aChildren

    func reset
        bIsRunning = false
        nLastResult = NODE_FAILURE
        for oChild in aChildren {
            oChild.reset()
        }

    func cleanup
        aChildren = []
        oParent = null

    func getName
        return cName

    func getID
        return cID

    func getLastResult
        return nLastResult

    func isRunning
        return bIsRunning

    private
        cName
        cID
        oParent
        aChildren
        nLastResult
        bIsRunning
}

# عقدة التسلسل - تنفذ الأطفال بالترتيب
class SequenceNode from BehaviorNode {
    
    func init cNodeName = "Sequence"
        super.init(cNodeName)
        nCurrentChildIndex = 0

    func execute nDeltaTime
        if len(aChildren) = 0 {
            return NODE_FAILURE
        }
        
        bIsRunning = true
        
        while nCurrentChildIndex < len(aChildren) {
            oCurrentChild = aChildren[nCurrentChildIndex + 1]
            nResult = oCurrentChild.execute(nDeltaTime)
            
            if nResult = NODE_FAILURE {
                reset()
                nLastResult = NODE_FAILURE
                bIsRunning = false
                return NODE_FAILURE
            elseif nResult = NODE_RUNNING
                nLastResult = NODE_RUNNING
                return NODE_RUNNING
            else  # NODE_SUCCESS
                nCurrentChildIndex++
            }
        }
        
        # جميع الأطفال نجحوا
        reset()
        nLastResult = NODE_SUCCESS
        bIsRunning = false
        return NODE_SUCCESS

    func reset
        super.reset()
        nCurrentChildIndex = 0

    private
        nCurrentChildIndex
}

# عقدة الاختيار - تنفذ أول طفل ناجح
class SelectorNode from BehaviorNode {
    
    func init cNodeName = "Selector"
        super.init(cNodeName)
        nCurrentChildIndex = 0

    func execute nDeltaTime
        if len(aChildren) = 0 {
            return NODE_FAILURE
        }
        
        bIsRunning = true
        
        while nCurrentChildIndex < len(aChildren) {
            oCurrentChild = aChildren[nCurrentChildIndex + 1]
            nResult = oCurrentChild.execute(nDeltaTime)
            
            if nResult = NODE_SUCCESS {
                reset()
                nLastResult = NODE_SUCCESS
                bIsRunning = false
                return NODE_SUCCESS
            elseif nResult = NODE_RUNNING
                nLastResult = NODE_RUNNING
                return NODE_RUNNING
            else  # NODE_FAILURE
                nCurrentChildIndex++
            }
        }
        
        # جميع الأطفال فشلوا
        reset()
        nLastResult = NODE_FAILURE
        bIsRunning = false
        return NODE_FAILURE

    func reset
        super.reset()
        nCurrentChildIndex = 0

    private
        nCurrentChildIndex
}

# عقدة العمل
class ActionNode from BehaviorNode {
    
    func init cNodeName, fpActionFunction
        super.init(cNodeName)
        fpAction = fpActionFunction

    func execute nDeltaTime
        if fpAction != null {
            bIsRunning = true
            nResult = call fpAction(nDeltaTime)
            nLastResult = nResult
            
            if nResult != NODE_RUNNING {
                bIsRunning = false
            }
            
            return nResult
        }
        
        nLastResult = NODE_FAILURE
        return NODE_FAILURE

    private
        fpAction
}

# عقدة الشرط
class ConditionNode from BehaviorNode {
    
    func init cNodeName, fpConditionFunction
        super.init(cNodeName)
        fpCondition = fpConditionFunction

    func execute nDeltaTime
        if fpCondition != null {
            bResult = call fpCondition()
            if bResult {
                nLastResult = NODE_SUCCESS
                return NODE_SUCCESS
            else
                nLastResult = NODE_FAILURE
                return NODE_FAILURE
            }
        }
        
        nLastResult = NODE_FAILURE
        return NODE_FAILURE

    private
        fpCondition
}
