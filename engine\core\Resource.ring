/*
كلاسات الموارد المساعدة لإدارة الموارد المختلفة
*/

# كلاس أساسي للموارد
class Resource {
    func init oRes, cResName, cResPath
        oResource = oRes
        cName = cResName
        cPath = cResPath
        nLoadTime = clock()

    func getResource
        return oResource

    func getName
        return cName

    func getPath
        return cPath

    func getLoadTime
        return nLoadTime

    private
        oResource
        cName
        cPath
        nLoadTime
}

# مورد القوام
class TextureResource from Resource {
    func init oTexture, cName, cPath
        super.init(oTexture, cName, cPath)
        cType = "Texture"

    func getWidth
        return oResource.width

    func getHeight
        return oResource.height

    func getFormat
        return oResource.format

    private
        cType
}

# مورد النموذج
class ModelResource from Resource {
    func init oModel, cName, cPath
        super.init(oModel, cName, cPath)
        cType = "Model"

    func getMeshCount
        return oResource.meshCount

    func getMaterialCount
        return oResource.materialCount

    private
        cType
}

# مورد الصوت
class SoundResource from Resource {
    func init oSound, cName, cPath
        super.init(oSound, cName, cPath)
        cType = "Sound"

    func getFrameCount
        return oResource.frameCount

    func getSampleRate
        return oResource.sampleRate

    private
        cType
}

# مورد الموسيقى
class MusicResource from Resource {
    func init oMusic, cName, cPath
        super.init(oMusic, cName, cPath)
        cType = "Music"

    func getFrameCount
        return oResource.frameCount

    func getSampleRate
        return oResource.sampleRate

    private
        cType
}

# مورد الخط
class FontResource from Resource {
    func init oFont, cName, cPath
        super.init(oFont, cName, cPath)
        cType = "Font"

    func getBaseSize
        return oResource.baseSize

    func getGlyphCount
        return oResource.glyphCount

    private
        cType
}
