/*
الكلاس: InputManager
الوصف: إدارة جميع مدخلات المستخدم من لوحة المفاتيح والفأرة ووحدة التحكم
المدخلات: أحداث المدخلات من النظام
المخرجات: حالة المدخلات ومعالجة الأحداث
*/

load "raylib.ring"

class InputManager {

    func init
        # حالة لوحة المفاتيح
        aKeyStates = []
        aPreviousKeyStates = []

        # حالة الفأرة
        aMouseButtonStates = []
        aPreviousMouseButtonStates = []
        aMousePosition = [0, 0]
        aPreviousMousePosition = [0, 0]
        nMouseWheelMove = 0

        # حالة وحدة التحكم
        aGamepadStates = []

        # خرائط المدخلات
        aInputMappings = []
        aInputListeners = []

        # إعداد المدخلات الافتراضية
        setupDefaultMappings()

    func setupDefaultMappings
        # إعداد خرائط المدخلات الافتراضية
        addInputMapping("MoveForward", KEY_W)
        addInputMapping("MoveBackward", KEY_S)
        addInputMapping("MoveLeft", KEY_A)
        addInputMapping("MoveRight", KEY_D)
        addInputMapping("Jump", KEY_SPACE)
        addInputMapping("Run", KEY_LEFT_SHIFT)
        addInputMapping("Crouch", KEY_LEFT_CONTROL)
        addInputMapping("Interact", KEY_E)
        addInputMapping("Menu", KEY_ESCAPE)

    func update
        # حفظ الحالة السابقة
        aPreviousKeyStates = aKeyStates
        aPreviousMouseButtonStates = aMouseButtonStates
        aPreviousMousePosition = aMousePosition

        # تحديث حالة لوحة المفاتيح
        updateKeyStates()

        # تحديث حالة الفأرة
        updateMouseStates()

        # تحديث حالة وحدة التحكم
        updateGamepadStates()

        # معالجة أحداث الإدخال
        processInputEvents()

    func updateKeyStates
        aKeyStates = []
        # فحص جميع المفاتيح المهمة
        aImportantKeys = [
            KEY_W, KEY_A, KEY_S, KEY_D, KEY_SPACE, KEY_ESCAPE,
            KEY_LEFT_SHIFT, KEY_LEFT_CONTROL, KEY_E, KEY_F,
            KEY_TAB, KEY_ENTER, KEY_BACKSPACE, KEY_DELETE,
            KEY_LEFT, KEY_RIGHT, KEY_UP, KEY_DOWN,
            KEY_F1, KEY_F2, KEY_F3, KEY_F4, KEY_F5
        ]

        for nKey in aImportantKeys {
            aKeyStates[nKey] = IsKeyDown(nKey)
        }

    func updateMouseStates
        aMousePosition = [GetMouseX(), GetMouseY()]
        nMouseWheelMove = GetMouseWheelMove()

        aMouseButtonStates = []
        aMouseButtonStates[MOUSE_LEFT_BUTTON] = IsMouseButtonDown(MOUSE_LEFT_BUTTON)
        aMouseButtonStates[MOUSE_RIGHT_BUTTON] = IsMouseButtonDown(MOUSE_RIGHT_BUTTON)
        aMouseButtonStates[MOUSE_MIDDLE_BUTTON] = IsMouseButtonDown(MOUSE_MIDDLE_BUTTON)

    func updateGamepadStates
        # تحديث حالة وحدة التحكم (إذا كانت متصلة)
        if IsGamepadAvailable(0) {
            aGamepadStates = []
            # فحص أزرار وحدة التحكم
            aGamepadStates[GAMEPAD_BUTTON_RIGHT_FACE_DOWN] = IsGamepadButtonDown(0, GAMEPAD_BUTTON_RIGHT_FACE_DOWN)
            aGamepadStates[GAMEPAD_BUTTON_RIGHT_FACE_RIGHT] = IsGamepadButtonDown(0, GAMEPAD_BUTTON_RIGHT_FACE_RIGHT)
            aGamepadStates[GAMEPAD_BUTTON_RIGHT_FACE_LEFT] = IsGamepadButtonDown(0, GAMEPAD_BUTTON_RIGHT_FACE_LEFT)
            aGamepadStates[GAMEPAD_BUTTON_RIGHT_FACE_UP] = IsGamepadButtonDown(0, GAMEPAD_BUTTON_RIGHT_FACE_UP)
        }

    func processInputEvents
        # معالجة أحداث الإدخال
        for oMapping in aInputMappings {
            if isActionTriggered(oMapping) {
                notifyListeners(oMapping.getAction())
            }
        }

    func isActionTriggered oMapping
        return isKeyPressed(oMapping.getKey()) or
               (oMapping.hasGamepadButton() and
                IsGamepadButtonPressed(0, oMapping.getGamepadButton()))

    func notifyListeners cAction
        for oListener in aInputListeners {
            oListener.onInputEvent(cAction)
        }

    func isKeyDown nKey
        if aKeyStates[nKey] != null {
            return aKeyStates[nKey]
        }
        return IsKeyDown(nKey)

    func isKeyPressed nKey
        return IsKeyPressed(nKey)

    func isKeyReleased nKey
        return IsKeyReleased(nKey)

    func isMouseButtonDown nButton
        if aMouseButtonStates[nButton] != null {
            return aMouseButtonStates[nButton]
        }
        return IsMouseButtonDown(nButton)

    func isMouseButtonPressed nButton
        return IsMouseButtonPressed(nButton)

    func isMouseButtonReleased nButton
        return IsMouseButtonReleased(nButton)

    func getMousePosition
        return aMousePosition

    func getMouseDelta
        return [aMousePosition[1] - aPreviousMousePosition[1],
                aMousePosition[2] - aPreviousMousePosition[2]]

    func getMouseWheelMove
        return nMouseWheelMove

    func addInputMapping cAction, nKey
        aInputMappings[cAction] = new InputMapping(cAction, nKey)

    func removeInputMapping cAction
        del(aInputMappings, cAction)

    func addEventListener oListener
        add(aInputListeners, oListener)

    func removeEventListener oListener
        del(aInputListeners, find(aInputListeners, oListener))

    func getGamepadAxisMovement nGamepad, nAxis
        return GetGamepadAxisMovement(nGamepad, nAxis)

    func isActionPressed cAction
        if aInputMappings[cAction] != null {
            oMapping = aInputMappings[cAction]
            return isKeyPressed(oMapping.getKey())
        }
        return false

    func isActionDown cAction
        if aInputMappings[cAction] != null {
            oMapping = aInputMappings[cAction]
            return isKeyDown(oMapping.getKey())
        }
        return false

    private
        aKeyStates
        aPreviousKeyStates
        aMouseButtonStates
        aPreviousMouseButtonStates
        aMousePosition
        aPreviousMousePosition
        nMouseWheelMove
        aGamepadStates
        aInputMappings
        aInputListeners
}
