/*
مثال: عرض الجسيمات
الوصف: مثال يوضح إمكانيات نظام الجسيمات
المدخلات: تفاعل المستخدم
المخرجات: تأثيرات جسيمات متنوعة
*/

load "../../game_engine.ring"

# تشغيل العرض
oDemo = new ParticlesDemo()
oDemo.start()

class ParticlesDemo {
    
    func init
        # إنشاء المحرك
        oEngine = new Engine(1280, 720, "عرض الجسيمات - Ring Game Engine")
        
        # متغيرات العرض
        aEmitters = []
        nCurrentEffect = 1
        nMaxEffects = 6
        aEffectNames = [
            "نار", "دخان", "انفجار", "ماء", "سحر", "شرارات"
        ]
        
        # إعداد العرض
        setupDemo()

    func setupDemo
        ? "إعداد عرض الجسيمات..."
        
        # إنشاء المشهد
        createScene()
        
        # إعداد الإضاءة
        setupLighting()
        
        # إعداد الكاميرا
        setupCamera()
        
        # إنشاء مولدات الجسيمات
        createParticleEmitters()
        
        ? "تم إعداد عرض الجسيمات"

    func createScene
        oMainScene = new Scene("ParticlesDemo")
        oEngine.getSceneManager().setCurrentScene(oMainScene)
        
        # إنشاء أرضية بسيطة
        oGround = new GameObject("Ground")
        oGround.setPosition([0, -2, 0])
        oGround.setScale([20, 1, 20])
        
        oGroundMesh = new MeshRenderer()
        oGroundMesh.createPlane(20, 20)
        oGroundMesh.setMaterial(createGroundMaterial())
        oGround.addComponent(oGroundMesh)
        
        oMainScene.addGameObject(oGround)

    func setupLighting
        # إضاءة رئيسية
        oMainLight = new Light3D([0, 15, 5], WHITE, LIGHT_DIRECTIONAL)
        oMainLight.setIntensity(0.8)
        oMainLight.setDirection([0, -1, -0.5])
        oEngine.getSceneManager().getCurrentScene().addLight(oMainLight)
        
        # إضاءة محيطة ناعمة
        oAmbientLight = new Light3D([0, 10, 0], Color(100, 150, 200, 255), LIGHT_POINT)
        oAmbientLight.setIntensity(0.3)
        oAmbientLight.setRange(25.0)
        oEngine.getSceneManager().getCurrentScene().addLight(oAmbientLight)

    func setupCamera
        oCamera = new Camera([0, 5, 15], [0, 2, 0], [0, 1, 0])
        oCamera.setControlMode(CAMERA_CONTROL_ORBITAL)
        oCamera.setMovementSpeed(8.0)
        
        oEngine.getSceneManager().getCurrentScene().addCamera(oCamera)
        oEngine.getSceneManager().getCurrentScene().setActiveCamera(oCamera)

    func createParticleEmitters
        # إنشاء مولدات جسيمات مختلفة
        
        # مولد النار
        oFireEmitter = createFireEffect([-6, 0, 0])
        add(aEmitters, oFireEmitter)
        
        # مولد الدخان
        oSmokeEmitter = createSmokeEffect([-3, 0, 0])
        add(aEmitters, oSmokeEmitter)
        
        # مولد الانفجار
        oExplosionEmitter = createExplosionEffect([0, 0, 0])
        add(aEmitters, oExplosionEmitter)
        
        # مولد الماء
        oWaterEmitter = createWaterEffect([3, 3, 0])
        add(aEmitters, oWaterEmitter)
        
        # مولد السحر
        oMagicEmitter = createMagicEffect([6, 1, 0])
        add(aEmitters, oMagicEmitter)
        
        # مولد الشرارات
        oSparksEmitter = createSparksEffect([0, 0, 3])
        add(aEmitters, oSparksEmitter)
        
        # تفعيل المولد الأول فقط
        activateEffect(1)

    func createFireEffect aPos
        oEmitter = oEngine.getParticleSystem().createEmitter("fire", aPos)
        
        # إعدادات النار
        oEmitter.setParticlesPerSecond(150)
        oEmitter.setLifetime(2.0)
        oEmitter.setStartSpeed(3.0)
        oEmitter.setSpread(25.0)
        oEmitter.setStartSize(0.3)
        oEmitter.setEndSize(1.2)
        oEmitter.setStartColor([1.0, 0.8, 0.2, 1.0])
        oEmitter.setEndColor([1.0, 0.2, 0.0, 0.0])
        oEmitter.setGravityAffected(false)
        
        return oEmitter

    func createSmokeEffect aPos
        oEmitter = oEngine.getParticleSystem().createEmitter("smoke", aPos)
        
        # إعدادات الدخان
        oEmitter.setParticlesPerSecond(60)
        oEmitter.setLifetime(4.0)
        oEmitter.setStartSpeed(1.5)
        oEmitter.setSpread(45.0)
        oEmitter.setStartSize(0.5)
        oEmitter.setEndSize(2.5)
        oEmitter.setStartColor([0.3, 0.3, 0.3, 0.8])
        oEmitter.setEndColor([0.6, 0.6, 0.6, 0.0])
        oEmitter.setGravityAffected(false)
        
        return oEmitter

    func createExplosionEffect aPos
        oEmitter = oEngine.getParticleSystem().createEmitter("explosion", aPos)
        
        # إعدادات الانفجار
        oEmitter.setParticlesPerSecond(0)  # لا انبعاث مستمر
        oEmitter.setBurstCount(300)        # انفجار واحد
        oEmitter.setBurstInterval(3.0)     # كل 3 ثوان
        oEmitter.setLifetime(2.5)
        oEmitter.setStartSpeed(12.0)
        oEmitter.setSpread(180.0)
        oEmitter.setStartSize(0.2)
        oEmitter.setEndSize(0.05)
        oEmitter.setStartColor([1.0, 0.6, 0.0, 1.0])
        oEmitter.setEndColor([1.0, 0.0, 0.0, 0.0])
        
        return oEmitter

    func createWaterEffect aPos
        oEmitter = oEngine.getParticleSystem().createEmitter("water", aPos)
        
        # إعدادات الماء
        oEmitter.setParticlesPerSecond(200)
        oEmitter.setLifetime(3.0)
        oEmitter.setStartSpeed(6.0)
        oEmitter.setSpread(20.0)
        oEmitter.setStartSize(0.15)
        oEmitter.setEndSize(0.08)
        oEmitter.setStartColor([0.2, 0.6, 1.0, 0.9])
        oEmitter.setEndColor([0.1, 0.4, 0.8, 0.3])
        oEmitter.setGravityAffected(true)
        oEmitter.setBounceStrength(0.4)
        
        return oEmitter

    func createMagicEffect aPos
        oEmitter = oEngine.getParticleSystem().createEmitter("magic", aPos)
        
        # إعدادات السحر
        oEmitter.setParticlesPerSecond(100)
        oEmitter.setLifetime(3.0)
        oEmitter.setStartSpeed(2.0)
        oEmitter.setSpread(90.0)
        oEmitter.setStartSize(0.3)
        oEmitter.setEndSize(0.6)
        oEmitter.setStartColor([0.8, 0.2, 1.0, 1.0])
        oEmitter.setEndColor([0.4, 0.8, 1.0, 0.0])
        oEmitter.setGravityAffected(false)
        oEmitter.setRotationSpeed(180.0)
        
        return oEmitter

    func createSparksEffect aPos
        oEmitter = oEngine.getParticleSystem().createEmitter("sparks", aPos)
        
        # إعدادات الشرارات
        oEmitter.setParticlesPerSecond(250)
        oEmitter.setLifetime(1.5)
        oEmitter.setStartSpeed(8.0)
        oEmitter.setSpread(60.0)
        oEmitter.setStartSize(0.08)
        oEmitter.setEndSize(0.03)
        oEmitter.setStartColor([1.0, 1.0, 0.2, 1.0])
        oEmitter.setEndColor([1.0, 0.3, 0.0, 0.0])
        oEmitter.setGravityAffected(true)
        
        return oEmitter

    func activateEffect nIndex
        # تفعيل تأثير معين وإلغاء الباقي
        for i = 1 to len(aEmitters) {
            if i = nIndex {
                aEmitters[i].setActive(true)
            else
                aEmitters[i].setActive(false)
            }
        }
        
        nCurrentEffect = nIndex
        ? "تم تفعيل تأثير: " + aEffectNames[nIndex]

    func update nDeltaTime
        # معالجة المدخلات
        handleInput()
        
        # تحديث التأثيرات الخاصة
        updateSpecialEffects(nDeltaTime)

    func handleInput
        # تغيير التأثيرات بالأرقام
        for i = 1 to nMaxEffects {
            if IsKeyPressed(KEY_ONE + i - 1) {
                activateEffect(i)
            }
        }
        
        # التنقل بين التأثيرات
        if IsKeyPressed(KEY_RIGHT) or IsKeyPressed(KEY_DOWN) {
            nCurrentEffect++
            if nCurrentEffect > nMaxEffects {
                nCurrentEffect = 1
            }
            activateEffect(nCurrentEffect)
        }
        
        if IsKeyPressed(KEY_LEFT) or IsKeyPressed(KEY_UP) {
            nCurrentEffect--
            if nCurrentEffect < 1 {
                nCurrentEffect = nMaxEffects
            }
            activateEffect(nCurrentEffect)
        }
        
        # إيقاف/تشغيل جميع التأثيرات
        if IsKeyPressed(KEY_SPACE) {
            toggleAllEffects()
        }
        
        # إعادة تشغيل التأثير الحالي
        if IsKeyPressed(KEY_R) {
            restartCurrentEffect()
        }
        
        # تغيير شدة الرياح
        if IsKeyDown(KEY_W) {
            oEngine.getParticleSystem().setWindForce([2.0, 0, 0])
        elseif IsKeyDown(KEY_S)
            oEngine.getParticleSystem().setWindForce([-2.0, 0, 0])
        else
            oEngine.getParticleSystem().setWindForce([0, 0, 0])
        }

    func updateSpecialEffects nDeltaTime
        # تحديثات خاصة للتأثيرات
        
        # تحريك مولد السحر في دائرة
        if nCurrentEffect = 5 and len(aEmitters) >= 5 {
            nTime = GetTime()
            aNewPos = [
                6 + 2 * cos(nTime),
                1 + sin(nTime * 2) * 0.5,
                2 * sin(nTime)
            ]
            aEmitters[5].setPosition(aNewPos)
        }

    func toggleAllEffects
        # تبديل حالة جميع التأثيرات
        bAnyActive = false
        for oEmitter in aEmitters {
            if oEmitter.isActive() {
                bAnyActive = true
                break
            }
        }
        
        for oEmitter in aEmitters {
            oEmitter.setActive(not bAnyActive)
        }
        
        if bAnyActive {
            ? "تم إيقاف جميع التأثيرات"
        else
            ? "تم تفعيل جميع التأثيرات"
        }

    func restartCurrentEffect
        # إعادة تشغيل التأثير الحالي
        if nCurrentEffect >= 1 and nCurrentEffect <= len(aEmitters) {
            oEmitter = aEmitters[nCurrentEffect]
            oEmitter.setActive(false)
            # انتظار قصير ثم إعادة التفعيل
            oEmitter.setActive(true)
            ? "تم إعادة تشغيل: " + aEffectNames[nCurrentEffect]
        }

    func createGroundMaterial
        oMaterial = new Material("GroundMaterial")
        oMaterial.setAlbedoColor(Color(60, 60, 80, 255))
        oMaterial.setRoughness(0.8)
        oMaterial.setMetallic(0.1)
        return oMaterial

    func renderUI
        # رسم واجهة المستخدم
        nY = 10
        DrawText("عرض الجسيمات - Ring Game Engine", 10, nY, 24, WHITE)
        nY += 35
        
        DrawText("التحكم:", 10, nY, 20, YELLOW)
        nY += 25
        DrawText("1-6: اختيار التأثير", 10, nY, 18, WHITE)
        nY += 22
        DrawText("الأسهم: التنقل بين التأثيرات", 10, nY, 18, WHITE)
        nY += 22
        DrawText("مسطرة المسافة: تبديل جميع التأثيرات", 10, nY, 18, WHITE)
        nY += 22
        DrawText("R: إعادة تشغيل التأثير الحالي", 10, nY, 18, WHITE)
        nY += 22
        DrawText("W/S: تغيير الرياح", 10, nY, 18, WHITE)
        nY += 35
        
        DrawText("التأثير الحالي: " + aEffectNames[nCurrentEffect], 10, nY, 20, GREEN)
        nY += 25
        
        # إحصائيات الجسيمات
        oParticleSystem = oEngine.getParticleSystem()
        if oParticleSystem != null {
            DrawText("الجسيمات النشطة: " + string(oParticleSystem.getActiveParticles()), 10, nY, 18, CYAN)
            nY += 22
            DrawText("المولدات النشطة: " + string(oParticleSystem.getActiveEmitters()), 10, nY, 18, CYAN)
        }

    func start
        ? "بدء عرض الجسيمات..."
        ? "استخدم الأرقام 1-6 لاختيار التأثيرات المختلفة"
        ? "استخدم الأسهم للتنقل بين التأثيرات"
        
        oEngine.start()

    private
        oEngine
        aEmitters
        nCurrentEffect
        nMaxEffects
        aEffectNames
}

