# دليل المطور - محرك الألعاب Ring Game Engine

## 📖 مقدمة

مرحباً بك في دليل المطور لمحرك الألعاب Ring Game Engine! هذا الدليل سيأخذك خطوة بخطوة من البداية حتى إنشاء ألعاب احترافية.

## 🎯 الأهداف التعليمية

بعد قراءة هذا الدليل ستتمكن من:
- فهم هيكل المحرك وأنظمته
- إنشاء مشاهد وكائنات تفاعلية
- استخدام أنظمة الفيزياء والذكاء الاصطناعي
- تطوير ألعاب كاملة ومتقدمة

## 🏗️ هيكل المحرك

### النظرة العامة
محرك الألعاب Ring Game Engine مبني على هيكل معياري يتكون من:

```
Engine (المحرك الرئيسي)
├── Core Systems (الأنظمة الأساسية)
│   ├── Scene Management (إدارة المشاهد)
│   ├── GameObject System (نظام الكائنات)
│   ├── Component System (نظام المكونات)
│   └── Resource Management (إدارة الموارد)
├── Rendering (العرض)
│   ├── 3D Graphics (الرسوميات ثلاثية الأبعاد)
│   ├── Lighting (الإضاءة)
│   ├── Materials (المواد)
│   └── Post-Processing (المعالجة اللاحقة)
├── Physics (الفيزياء)
│   ├── Rigid Bodies (الأجسام الصلبة)
│   ├── Collision Detection (كشف التصادمات)
│   └── Forces & Constraints (القوى والقيود)
├── Audio (الصوت)
│   ├── 3D Audio (الصوت ثلاثي الأبعاد)
│   ├── Music System (نظام الموسيقى)
│   └── Sound Effects (المؤثرات الصوتية)
├── AI (الذكاء الاصطناعي)
│   ├── Behavior Trees (أشجار السلوك)
│   ├── State Machines (آلات الحالة)
│   ├── Pathfinding (البحث عن المسار)
│   └── Perception (الإدراك)
├── Particles (الجسيمات)
│   ├── Emitters (المولدات)
│   ├── Physics Simulation (المحاكاة الفيزيائية)
│   └── Visual Effects (التأثيرات البصرية)
└── Input (المدخلات)
    ├── Keyboard & Mouse (لوحة المفاتيح والفأرة)
    ├── Gamepad Support (دعم وحدات التحكم)
    └── Input Mapping (خرائط المدخلات)
```

## 🚀 البدء السريع

### 1. إنشاء مشروع جديد

```ring
# تحميل المحرك
load "game_engine.ring"

# إنشاء محرك جديد
oEngine = new Engine(1280, 720, "لعبتي الأولى")

# بدء تشغيل المحرك
oEngine.start()
```

### 2. إنشاء مشهد بسيط

```ring
# إنشاء مشهد جديد
oScene = new Scene("MainScene")

# إنشاء كائن مكعب
oCube = new GameObject("MyCube")
oCube.setPosition([0, 1, 0])

# إضافة مكون العرض
oMeshRenderer = new MeshRenderer()
oMeshRenderer.createCube(1.0)
oCube.addComponent(oMeshRenderer)

# إضافة الكائن للمشهد
oScene.addGameObject(oCube)

# تعيين المشهد الحالي
oEngine.getSceneManager().setCurrentScene(oScene)
```

### 3. إضافة التفاعل

```ring
# إضافة مكون التحكم
oPlayerController = new PlayerController()
oPlayerController.setMovementSpeed(5.0)
oCube.addComponent(oPlayerController)

# إضافة مكون الفيزياء
oRigidBody = new RigidBody()
oRigidBody.setMass(1.0)
oCube.addComponent(oRigidBody)

oCollider = new BoxCollider()
oCube.addComponent(oCollider)
```

## 🎮 نظام الكائنات والمكونات

### GameObject (كائن اللعبة)
كائن اللعبة هو الوحدة الأساسية في المحرك. كل شيء في اللعبة هو كائن.

```ring
# إنشاء كائن جديد
oGameObject = new GameObject("MyObject")

# تعيين الخصائص الأساسية
oGameObject.setPosition([x, y, z])
oGameObject.setRotation([rx, ry, rz])
oGameObject.setScale([sx, sy, sz])

# تفعيل/إلغاء تفعيل الكائن
oGameObject.setActive(true)
oGameObject.setVisible(true)
```

### Component System (نظام المكونات)
المكونات تضيف وظائف للكائنات:

#### MeshRenderer (عارض الشبكة)
```ring
oMeshRenderer = new MeshRenderer()

# إنشاء أشكال أساسية
oMeshRenderer.createCube(1.0)
oMeshRenderer.createSphere(0.5)
oMeshRenderer.createPlane(10.0, 10.0)

# تحميل نموذج من ملف
oMeshRenderer.loadModel("assets/models/character.obj")

# تطبيق مادة
oMaterial = new Material("MyMaterial")
oMaterial.setAlbedoColor(Color(255, 100, 100, 255))
oMeshRenderer.setMaterial(oMaterial)
```

#### RigidBody (الجسم الصلب)
```ring
oRigidBody = new RigidBody()
oRigidBody.setMass(1.0)
oRigidBody.setRestitution(0.5)  # الارتداد
oRigidBody.setFriction(0.7)     # الاحتكاك
oRigidBody.setStatic(false)     # متحرك أم ثابت

# تطبيق قوى
oRigidBody.addForce([0, 10, 0])  # قوة لأعلى
oRigidBody.setVelocity([5, 0, 0])  # سرعة أولية
```

#### Collider (كاشف التصادم)
```ring
# كاشف مكعب
oBoxCollider = new BoxCollider()
oBoxCollider.setSize([2, 2, 2])

# كاشف كرة
oSphereCollider = new SphereCollider()
oSphereCollider.setRadius(1.0)

# كاشف مخصص
oMeshCollider = new MeshCollider()
oMeshCollider.setMesh(oCustomMesh)

# تعيين كمحفز (لا يوقف الحركة)
oCollider.setTrigger(true)
```

## 🎨 نظام العرض والرسوميات

### إدارة الكاميرا
```ring
# إنشاء كاميرا
oCamera = new Camera([0, 5, 10], [0, 0, 0], [0, 1, 0])

# أنماط التحكم المختلفة
oCamera.setControlMode(CAMERA_CONTROL_FREE)        # حرة
oCamera.setControlMode(CAMERA_CONTROL_ORBITAL)     # مدارية
oCamera.setControlMode(CAMERA_CONTROL_FIRST_PERSON) # شخص أول
oCamera.setControlMode(CAMERA_CONTROL_THIRD_PERSON) # شخص ثالث

# إعدادات الكاميرا
oCamera.setFieldOfView(45.0)
oCamera.setMovementSpeed(5.0)
oCamera.setMouseSensitivity(0.003)
```

### نظام الإضاءة
```ring
# إضاءة اتجاهية (الشمس)
oDirectionalLight = new Light3D([0, 10, 0], WHITE, LIGHT_DIRECTIONAL)
oDirectionalLight.setDirection([0, -1, -0.3])
oDirectionalLight.setIntensity(1.2)

# إضاءة نقطية
oPointLight = new Light3D([5, 3, 5], Color(255, 200, 150, 255), LIGHT_POINT)
oPointLight.setRange(15.0)
oPointLight.setAttenuation(1.0)

# إضاءة مخروطية (كشاف)
oSpotLight = new Light3D([0, 5, 0], WHITE, LIGHT_SPOT)
oSpotLight.setDirection([0, -1, 0])
oSpotLight.setInnerCone(30.0)
oSpotLight.setOuterCone(45.0)

# إضافة للمشهد
oScene.addLight(oDirectionalLight)
```

### نظام المواد
```ring
# إنشاء مادة جديدة
oMaterial = new Material("MetalMaterial")

# خصائص الألوان
oMaterial.setAlbedoColor(Color(150, 150, 150, 255))
oMaterial.setEmissiveColor(Color(50, 50, 100, 255))
oMaterial.setSpecularColor(WHITE)

# خصائص PBR
oMaterial.setRoughness(0.3)    # الخشونة (0 = أملس، 1 = خشن)
oMaterial.setMetallic(0.8)     # المعدنية (0 = عازل، 1 = معدن)
oMaterial.setEmissiveStrength(0.2)  # قوة الإضاءة الذاتية

# القوام
oMaterial.setAlbedoTexture(LoadTexture("metal_diffuse.png"))
oMaterial.setNormalTexture(LoadTexture("metal_normal.png"))
oMaterial.setRoughnessTexture(LoadTexture("metal_roughness.png"))

# خصائص الشفافية
oMaterial.setAlpha(0.8)
oMaterial.setTransparent(true)
```

## ⚡ نظام الفيزياء

### إعداد العالم الفيزيائي
```ring
# الحصول على محرك الفيزياء
oPhysicsEngine = oEngine.getPhysicsEngine()

# إعدادات عامة
oPhysicsEngine.setGravity([0, -9.81, 0])
oPhysicsEngine.setTimeStep(1.0/60.0)
oPhysicsEngine.setMaxSubSteps(10)
```

### إنشاء أجسام فيزيائية
```ring
# جسم ديناميكي (متحرك)
oDynamicBody = new RigidBody()
oDynamicBody.setMass(1.0)
oDynamicBody.setRestitution(0.6)  # ارتداد
oDynamicBody.setFriction(0.5)     # احتكاك
oDynamicBody.setLinearDamping(0.1)  # مقاومة الحركة الخطية
oDynamicBody.setAngularDamping(0.1) # مقاومة الدوران

# جسم ثابت (غير متحرك)
oStaticBody = new RigidBody()
oStaticBody.setStatic(true)
oStaticBody.setMass(0)

# جسم حركي (يتحرك لكن لا يتأثر بالقوى)
oKinematicBody = new RigidBody()
oKinematicBody.setKinematic(true)
```

### كشف التصادمات
```ring
# معالج التصادمات
class CollisionHandler {
    func onCollisionEnter oObject1, oObject2
        ? "تصادم بين " + oObject1.getName() + " و " + oObject2.getName()
        
        # مثال: تدمير الكائن عند التصادم
        if oObject2.getName() = "Bullet" {
            oObject1.destroy()
        }
    
    func onCollisionStay oObject1, oObject2
        # أثناء التصادم المستمر
    
    func onCollisionExit oObject1, oObject2
        # عند انتهاء التصادم
}

# تسجيل معالج التصادمات
oCollisionHandler = new CollisionHandler()
oPhysicsEngine.setCollisionHandler(oCollisionHandler)
```

## 🔊 نظام الصوت

### تشغيل الأصوات
```ring
# الحصول على محرك الصوت
oAudioEngine = oEngine.getAudioEngine()

# تحميل وتشغيل صوت
oSound = oAudioEngine.loadSound("assets/sounds/explosion.wav")
oAudioEngine.playSound(oSound)

# تشغيل موسيقى خلفية
oMusic = oAudioEngine.loadMusic("assets/music/background.mp3")
oAudioEngine.playMusic(oMusic, true)  # true = تكرار
```

### الصوت ثلاثي الأبعاد
```ring
# إنشاء مصدر صوت ثلاثي الأبعاد
oAudioSource = new AudioSource()
oAudioSource.setSound(oSound)
oAudioSource.setPosition([5, 0, 0])
oAudioSource.setVolume(0.8)
oAudioSource.setPitch(1.0)
oAudioSource.setLoop(false)

# إضافة للكائن
oGameObject.addComponent(oAudioSource)

# إعداد المستمع (عادة الكاميرا)
oAudioListener = new AudioListener()
oCamera.addComponent(oAudioListener)
```

## 🤖 نظام الذكاء الاصطناعي

### إنشاء وكيل ذكي
```ring
# إنشاء وكيل ذكي
oAIAgent = new AIAgent("guard")
oAIAgent.setVisionRange(15.0)
oAIAgent.setHearingRange(20.0)
oAIAgent.setMovementSpeed(3.0)

# إضافة للكائن
oGuard.addComponent(oAIAgent)
```

### أشجار السلوك
```ring
# إنشاء شجرة سلوك
oBehaviorTree = new BehaviorTree("GuardBehavior")

# العقدة الجذر
oRootSelector = oBehaviorTree.createSelectorNode("Root")

# شرط: هل يوجد عدو؟
oEnemyCheck = oBehaviorTree.createConditionNode("CheckEnemy", 
    func() { 
        return detectEnemy(oGuard) 
    })

# عمل: مطاردة العدو
oChaseAction = oBehaviorTree.createActionNode("Chase",
    func(nDeltaTime) { 
        return chaseEnemy(oGuard, nDeltaTime) 
    })

# تسلسل: فحص ثم مطاردة
oChaseSequence = oBehaviorTree.createSequenceNode("ChaseSequence")
oChaseSequence.addChild(oEnemyCheck)
oChaseSequence.addChild(oChaseAction)

# عمل: دورية عادية
oPatrolAction = oBehaviorTree.createActionNode("Patrol",
    func(nDeltaTime) { 
        return patrol(oGuard, nDeltaTime) 
    })

# بناء الشجرة
oRootSelector.addChild(oChaseSequence)
oRootSelector.addChild(oPatrolAction)
oBehaviorTree.setRootNode(oRootSelector)

# ربط الشجرة بالوكيل
oAIAgent.setBehaviorTree(oBehaviorTree)
```

### آلات الحالة
```ring
# إنشاء آلة حالة
oStateMachine = new FiniteStateMachine("EnemyFSM")

# حالة الدورية
oPatrolState = new State("Patrol")
oPatrolState.onEnter = func() { ? "بدء الدورية" }
oPatrolState.onUpdate = func(nDeltaTime) { patrolArea(nDeltaTime) }
oPatrolState.onExit = func() { ? "انتهاء الدورية" }

# حالة المطاردة
oChaseState = new State("Chase")
oChaseState.onUpdate = func(nDeltaTime) { chasePlayer(nDeltaTime) }

# إضافة الحالات
oStateMachine.addState("Patrol", oPatrolState)
oStateMachine.addState("Chase", oChaseState)

# انتقالات الحالة
oPatrolToChase = new Transition(oPatrolState, oChaseState,
    func() { return playerDetected() })
oChaseToPatrol = new Transition(oChaseState, oPatrolState,
    func() { return playerLost() })

oPatrolState.addTransition(oPatrolToChase)
oChaseState.addTransition(oChaseToPatrol)

# تعيين الحالة الأولية
oStateMachine.setInitialState("Patrol")

# ربط بالوكيل
oAIAgent.setStateMachine(oStateMachine)
```

## ✨ نظام الجسيمات

### إنشاء مولد جسيمات
```ring
# الحصول على نظام الجسيمات
oParticleSystem = oEngine.getParticleSystem()

# إنشاء مولد نار
oFireEmitter = oParticleSystem.createEmitter("fire", [0, 1, 0])
oFireEmitter.setParticlesPerSecond(100)
oFireEmitter.setLifetime(2.0)
oFireEmitter.setStartSize(0.3)
oFireEmitter.setEndSize(1.0)
oFireEmitter.setStartColor([1.0, 0.8, 0.2, 1.0])
oFireEmitter.setEndColor([1.0, 0.2, 0.0, 0.0])

# إنشاء مولد انفجار
oExplosionEmitter = oParticleSystem.createEmitter("explosion", [0, 0, 0])
oExplosionEmitter.setBurstCount(500)  # انفجار واحد
oExplosionEmitter.setLifetime(3.0)
oExplosionEmitter.setStartSpeed(15.0)
oExplosionEmitter.setSpread(180.0)
```

### تخصيص الجسيمات
```ring
# إعدادات متقدمة
oEmitter.setGravityAffected(true)
oEmitter.setWindAffected(true)
oEmitter.setBounceStrength(0.5)
oEmitter.setRotationSpeed(90.0)

# تأثيرات الرياح
oParticleSystem.setWindForce([2.0, 0, 0])

# قوى عامة
oParticleSystem.addGlobalForce([0, -5.0, 0])  # جاذبية إضافية
```

## 🎮 نظام المدخلات

### إعداد المدخلات
```ring
# الحصول على مدير المدخلات
oInputManager = oEngine.getInputManager()

# خرائط المدخلات
oInputManager.addInputMapping("MoveForward", KEY_W)
oInputManager.addInputMapping("MoveBackward", KEY_S)
oInputManager.addInputMapping("MoveLeft", KEY_A)
oInputManager.addInputMapping("MoveRight", KEY_D)
oInputManager.addInputMapping("Jump", KEY_SPACE)
oInputManager.addInputMapping("Fire", MOUSE_LEFT_BUTTON)

# دعم وحدة التحكم
oInputManager.addInputMapping("MoveForward", GAMEPAD_BUTTON_LEFT_THUMB_UP)
oInputManager.addInputMapping("Jump", GAMEPAD_BUTTON_A)
```

### معالجة المدخلات
```ring
class PlayerController from Component {
    func update nDeltaTime
        # حركة اللاعب
        if oInputManager.isActionDown("MoveForward") {
            moveForward(nDeltaTime)
        }
        if oInputManager.isActionPressed("Jump") {
            jump()
        }
        
        # الفأرة
        aMouseDelta = oInputManager.getMouseDelta()
        rotateCamera(aMouseDelta)
        
        # وحدة التحكم
        aLeftStick = oInputManager.getGamepadLeftStick()
        moveWithStick(aLeftStick, nDeltaTime)
    }
}
```

## 📁 إدارة الموارد

### تحميل الموارد
```ring
# الحصول على مدير الموارد
oResourceManager = oEngine.getResourceManager()

# تحميل النماذج
oModel = oResourceManager.loadModel("assets/models/character.fbx", "Character")

# تحميل القوام
oTexture = oResourceManager.loadTexture("assets/textures/wood.png", "Wood")

# تحميل الأصوات
oSound = oResourceManager.loadSound("assets/sounds/footstep.wav", "Footstep")

# تحميل الموسيقى
oMusic = oResourceManager.loadMusic("assets/music/theme.mp3", "MainTheme")
```

### إدارة الذاكرة
```ring
# تحميل مسبق للموارد
oResourceManager.preloadResources([
    "assets/models/player.fbx",
    "assets/textures/ground.png",
    "assets/sounds/ambient.wav"
])

# تنظيف الموارد غير المستخدمة
oResourceManager.cleanupUnusedResources()

# إلغاء تحميل مورد معين
oResourceManager.unloadResource("UnusedTexture")
```

## 🎬 إدارة المشاهد

### إنشاء مشاهد متعددة
```ring
# مشهد القائمة الرئيسية
oMenuScene = new Scene("MainMenu")
# ... إضافة عناصر القائمة

# مشهد اللعبة
oGameScene = new Scene("GameLevel1")
# ... إضافة كائنات اللعبة

# مشهد الإعدادات
oSettingsScene = new Scene("Settings")
# ... إضافة عناصر الإعدادات

# إضافة المشاهد للمدير
oSceneManager = oEngine.getSceneManager()
oSceneManager.addScene(oMenuScene)
oSceneManager.addScene(oGameScene)
oSceneManager.addScene(oSettingsScene)
```

### التنقل بين المشاهد
```ring
# تغيير فوري
oSceneManager.setCurrentScene(oGameScene)

# انتقال مع تأثير
oSceneManager.transitionToScene(oGameScene, 1.5)  # 1.5 ثانية

# التنقل بالاسم
oSceneManager.setCurrentSceneByName("GameLevel1")
```

### حفظ وتحميل المشاهد
```ring
# حفظ المشهد
oGameScene.save("assets/scenes/level1.json")

# تحميل مشهد
oLoadedScene = oSceneManager.loadScene("assets/scenes/level1.json")
```

## 🛠️ أدوات التطوير

### نظام التصحيح
```ring
# تفعيل معلومات التصحيح
oEngine.getRenderer().setShowStats(true)
oEngine.getRenderer().setShowBounds(true)
oEngine.getRenderer().setShowNormals(true)

# طباعة معلومات الكائن
? "موقع اللاعب: " + string(oPlayer.getPosition())
? "سرعة اللاعب: " + string(oPlayer.getVelocity())

# إحصائيات الأداء
oStats = oEngine.getRenderer().getStatistics()
? "مثلثات مرسومة: " + string(oStats[:trianglesRendered])
? "استدعاءات الرسم: " + string(oStats[:drawCalls])
```

### محرر المشاهد
```ring
# تشغيل محرر المشاهد
oSceneEditor = new SceneEditor()
oSceneEditor.loadScene("assets/scenes/level1.json")
oSceneEditor.start()
```

## 📊 تحسين الأداء

### نصائح عامة
1. **استخدم Object Pooling للكائنات المتكررة**
2. **قلل من عدد استدعاءات الرسم**
3. **استخدم Level of Detail (LOD)**
4. **فعل Frustum Culling**
5. **حسن استخدام القوام**

### مثال على Object Pooling
```ring
class BulletPool {
    func init nPoolSize
        aBullets = []
        for i = 1 to nPoolSize {
            oBullet = new GameObject("Bullet")
            oBullet.setActive(false)
            add(aBullets, oBullet)
        }
    
    func getBullet
        for oBullet in aBullets {
            if not oBullet.isActive() {
                oBullet.setActive(true)
                return oBullet
            }
        }
        return null  # المجمع ممتلئ
    
    func returnBullet oBullet
        oBullet.setActive(false)
}
```

## 🎯 الخطوات التالية

الآن بعد أن تعلمت الأساسيات، يمكنك:

1. **استكشاف الأمثلة** في مجلد `examples/`
2. **قراءة مرجع API** للتفاصيل الكاملة
3. **إنشاء لعبتك الأولى** باستخدام ما تعلمته
4. **الانضمام للمجتمع** للحصول على المساعدة والمشاركة

## 📚 موارد إضافية

- [مرجع API الكامل](api_reference.md)
- [دروس تعليمية متقدمة](tutorials/)
- [أمثلة عملية](../examples/)
- [أسئلة شائعة](faq.md)
- [استكشاف الأخطاء](troubleshooting.md)

---

**مبروك! أنت الآن جاهز لإنشاء ألعاب رائعة مع محرك الألعاب Ring Game Engine!** 🎮✨
