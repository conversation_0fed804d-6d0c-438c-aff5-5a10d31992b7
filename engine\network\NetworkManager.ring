load "socket.ring"

class NetworkManager {
    

    func init
        oNetworkState = new NetworkState

    # إدارة الخادم
    func startServer nPort
        oServer = new NetworkServer(nPort)
        oServer.onClientConnect = method(oClient) {
            add(aConnections, oClient)
            broadcastMessage("PlayerJoined", oClient.getId())
        }
        oServer.onClientDisconnect = method(oClient) {
            del(aConnections, find(aConnections, oClient))
            broadcastMessage("PlayerLeft", oClient.getId())
        }
        oServer.start()

    # إدارة العميل
    func connectToServer cIP, nPort
        oClient = new NetworkClient
        oClient.onConnect = method {
            println("Connected to server!")
        }
        oClient.onDisconnect = method {
            println("Disconnected from server!")
        }
        oClient.connect(cIP, nPort)

    # المزامنة والتكرار
    func registerReplicatedObject oObject
        add(aReplicationData, new ReplicatedObject(oObject))

    func unregisterReplicatedObject oObject
        for i = 1 to len(aReplicationData) {
            if aReplicationData[i].getObject() = oObject {
                del(aReplicationData, i)
                exit
            }
        }

    # معالجة الرسائل
    func registerMessageHandler cType, fpHandler
        add(aMessageHandlers, new MessageHandler(cType, fpHandler))

    func sendMessage cType, xData, nClientId = -1
        oMessage = new NetworkMessage(cType, xData)
        if nClientId = -1 {
            broadcastMessage(oMessage)
        } else {
            sendToClient(nClientId, oMessage)
        }

    func broadcastMessage oMessage
        for oConnection in aConnections {
            oConnection.send(oMessage)
        }

    # التزامن والتنبؤ
    func interpolateState oOldState, oNewState, nAlpha
        return oNetworkState.interpolate(oOldState, oNewState, nAlpha)

    func predictState oCurrentState, nDeltaTime
        return oNetworkState.predict(oCurrentState, nDeltaTime)

    func reconcileState oPredictedState, oActualState
        return oNetworkState.reconcile(oPredictedState, oActualState)

    # معالجة التأخير
    func addLagCompensation oHitbox, nPing
        oNetworkState.compensateLag(oHitbox, nPing)

    # التحديث والمعالجة
    func update
        nCurrentTime = clock()
        if nCurrentTime - nLastTick >= 1000/nTickRate {
            processNetworkTick()
            nLastTick = nCurrentTime
        }

        updateReplicatedObjects()
        processMessages()

    private
        oServer
        oClient
        aConnections = []
        aMessageHandlers = []
        nTickRate = 60
        nLastTick = 0
        aReplicationData = []
        oNetworkState

    func processNetworkTick
        if oServer {
            sendWorldState()
        }
        if oClient {
            sendInputs()
        }

    func updateReplicatedObjects
        for oReplicated in aReplicationData {
            if oReplicated.hasChanged() {
                broadcastUpdate(oReplicated)
            }
        }

    func processMessages
        while hasMessages() {
            oMessage = getNextMessage()
            for oHandler in aMessageHandlers {
                if oHandler.canHandle(oMessage) {
                    oHandler.handle(oMessage)
                }
            }
        }

    func cleanup
        if oServer {
            oServer.stop()
        }
        if oClient {
            oClient.disconnect()
        }
}
